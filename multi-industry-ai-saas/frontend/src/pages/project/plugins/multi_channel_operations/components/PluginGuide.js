import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Steps,
  Button,
  Space,
  Divider,
  Alert,
  Collapse,
  Tag,
  Row,
  Col,
  Timeline,
  Descriptions,
  Image,
  Tabs,
  List,
  Progress,
  Statistic,
  Badge
} from 'antd';
import {
  CheckCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  RocketOutlined,
  SettingOutlined,
  BookOutlined,
  PlayCircleOutlined,
  QuestionCircleOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  ShoppingOutlined,
  RobotOutlined,
  PictureOutlined,
  LinkOutlined,
  GiftOutlined,
  BarChartOutlined,
  EyeOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;
const { Panel } = Collapse;
const { TabPane } = Tabs;

const PluginGuide = ({ projectId }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [setupProgress, setSetupProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  // 功能特性数据
  const features = [
    {
      icon: <ShoppingOutlined style={{ color: '#1890ff' }} />,
      title: '智能商品管理',
      description: '一品多渠道同步，AI优化商品信息，批量操作管理',
      benefits: ['提升50%管理效率', '减少90%重复工作', '智能优化转化率']
    },
    {
      icon: <RobotOutlined style={{ color: '#722ed1' }} />,
      title: 'AI智能体助手',
      description: '对话式运营助手，自然语言执行各种运营任务',
      benefits: ['24/7智能服务', '自动化决策', '智能数据分析']
    },
    {
      icon: <PictureOutlined style={{ color: '#fa8c16' }} />,
      title: 'AI图片生成',
      description: '多模型支持，平台定制化图片生成，提升商品视觉效果',
      benefits: ['节省80%设计成本', '提升点击率', '品牌一致性']
    },
    {
      icon: <LinkOutlined style={{ color: '#52c41a' }} />,
      title: '智能类目映射',
      description: '自动映射不同平台类目，确保商品准确分类',
      benefits: ['100%准确映射', '自动同步更新', '减少分类错误']
    },
    {
      icon: <GiftOutlined style={{ color: '#eb2f96' }} />,
      title: '平台活动管理',
      description: '集中管理各平台活动，自动报名和效果跟踪',
      benefits: ['提升参与率', '增加补贴收入', '活动效果分析']
    },
    {
      icon: <BarChartOutlined style={{ color: '#13c2c2' }} />,
      title: '数据分析洞察',
      description: '全面的运营数据分析，智能建议和趋势预测',
      benefits: ['数据驱动决策', '趋势预测', '智能建议']
    },
    {
      icon: <ThunderboltOutlined style={{ color: '#faad14' }} />,
      title: '自动化规则',
      description: '可视化编辑，智能运营自动化，提升运营效率',
      benefits: ['自动化运营', '规则可视化', '智能触发']
    },
    {
      icon: <EyeOutlined style={{ color: '#f5222d' }} />,
      title: '竞品监控',
      description: '实时监控竞品价格和活动，智能预警和分析',
      benefits: ['实时监控', '智能预警', '竞争分析']
    }
  ];

  // 设置步骤
  const setupSteps = [
    {
      title: '基础配置',
      description: '配置AI模型和基本设置',
      content: (
        <div>
          <Alert
            message="配置AI模型"
            description="选择适合的AI模型，配置API密钥和参数"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Descriptions bordered>
            <Descriptions.Item label="对话模型">GPT-4 (推荐)</Descriptions.Item>
            <Descriptions.Item label="图片生成模型">DALL-E 3</Descriptions.Item>
            <Descriptions.Item label="温度参数">0.7</Descriptions.Item>
          </Descriptions>
        </div>
      )
    },
    {
      title: '平台连接',
      description: '连接各个销售平台',
      content: (
        <div>
          <Alert
            message="连接销售平台"
            description="配置各平台API凭证，建立安全连接"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <List
            dataSource={[
              { name: '美团外卖', status: 'connected', color: '#52c41a' },
              { name: '美团团购', status: 'pending', color: '#faad14' },
              { name: '饿了么', status: 'disconnected', color: '#f5222d' },
              { name: '抖音团购', status: 'connected', color: '#52c41a' }
            ]}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  title={item.name}
                  description={
                    <Badge
                      status={item.status === 'connected' ? 'success' : 
                             item.status === 'pending' ? 'processing' : 'error'}
                      text={item.status === 'connected' ? '已连接' : 
                            item.status === 'pending' ? '连接中' : '未连接'}
                    />
                  }
                />
              </List.Item>
            )}
          />
        </div>
      )
    },
    {
      title: '商品导入',
      description: '导入商品到系统',
      content: (
        <div>
          <Alert
            message="导入商品数据"
            description="从项目商品库导入商品，建立渠道映射关系"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Row gutter={16}>
            <Col span={8}>
              <Statistic title="可导入商品" value={156} />
            </Col>
            <Col span={8}>
              <Statistic title="已导入商品" value={89} />
            </Col>
            <Col span={8}>
              <Statistic title="导入进度" value={57} suffix="%" />
            </Col>
          </Row>
        </div>
      )
    },
    {
      title: '类目映射',
      description: '配置类目映射关系',
      content: (
        <div>
          <Alert
            message="配置类目映射"
            description="建立本地类目与各平台类目的映射关系"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Progress
            percent={75}
            status="active"
            format={() => '75% 已映射'}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              已完成主要类目映射，建议使用AI智能映射功能完成剩余类目
            </Text>
          </div>
        </div>
      )
    },
    {
      title: '完成设置',
      description: '开始使用插件功能',
      content: (
        <div>
          <Alert
            message="设置完成"
            description="恭喜！您已完成全渠道运营助手的基础设置"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button type="primary" size="large" block icon={<RocketOutlined />}>
              开始使用全渠道运营
            </Button>
            <Button size="large" block icon={<BookOutlined />}>
              查看使用教程
            </Button>
          </Space>
        </div>
      )
    }
  ];

  // 常见问题
  const faqData = [
    {
      question: '如何配置平台API凭证？',
      answer: '在渠道管理页面，点击"添加渠道"，选择对应平台，输入API密钥和相关配置信息。系统会自动测试连接状态。'
    },
    {
      question: 'AI图片生成需要额外费用吗？',
      answer: 'AI图片生成使用您配置的AI模型API，费用按照对应平台的计费标准。建议合理使用，避免不必要的成本。'
    },
    {
      question: '商品同步失败怎么办？',
      answer: '检查渠道连接状态、商品信息完整性、类目映射是否正确。可以在操作日志中查看详细错误信息。'
    },
    {
      question: '如何设置自动化规则？',
      answer: '在自动化规则页面，使用可视化编辑器拖拽组件创建规则。支持价格监控、库存预警、竞品分析等多种触发条件。'
    },
    {
      question: '竞品监控的数据来源是什么？',
      answer: '通过合规的数据采集方式获取公开的商品信息，包括价格、活动、评价等。确保数据的准确性和时效性。'
    }
  ];

  // 使用技巧
  const tips = [
    {
      icon: <BulbOutlined style={{ color: '#faad14' }} />,
      title: '批量操作技巧',
      content: '使用批量选择功能，可以同时对多个商品进行价格调整、渠道同步等操作，大幅提升工作效率。'
    },
    {
      icon: <RobotOutlined style={{ color: '#722ed1' }} />,
      title: 'AI助手使用',
      content: '与AI助手对话时，描述越详细，执行结果越准确。例如："将苹果类商品的价格调整为比竞品低5%"。'
    },
    {
      icon: <ThunderboltOutlined style={{ color: '#52c41a' }} />,
      title: '自动化规则优化',
      content: '合理设置触发条件和执行频率，避免过于频繁的操作。建议先在测试环境验证规则效果。'
    },
    {
      icon: <BarChartOutlined style={{ color: '#1890ff' }} />,
      title: '数据分析应用',
      content: '定期查看数据分析报告，关注关键指标变化趋势，及时调整运营策略。'
    }
  ];

  useEffect(() => {
    // 计算设置进度
    const progress = (currentStep / (setupSteps.length - 1)) * 100;
    setSetupProgress(progress);
  }, [currentStep]);

  return (
    <div>
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 插件概览 */}
          <TabPane tab="插件概览" key="overview">
            <div style={{ textAlign: 'center', marginBottom: 32 }}>
              <Title level={2}>
                <ThunderboltOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                全渠道运营助手
              </Title>
              <Paragraph style={{ fontSize: 16, color: '#666' }}>
                专业的全渠道运营管理平台，集成AI智能优化、自动化运营、数据分析等功能
              </Paragraph>
              <Space>
                <Tag color="blue">v2.0.0</Tag>
                <Tag color="green">已激活</Tag>
                <Tag color="orange">AI增强</Tag>
              </Space>
            </div>

            <Row gutter={[16, 16]}>
              {features.map((feature, index) => (
                <Col span={12} key={index}>
                  <Card size="small" hoverable>
                    <Card.Meta
                      avatar={feature.icon}
                      title={feature.title}
                      description={feature.description}
                    />
                    <div style={{ marginTop: 12 }}>
                      {feature.benefits.map((benefit, idx) => (
                        <Tag key={idx} color="blue" style={{ marginBottom: 4 }}>
                          {benefit}
                        </Tag>
                      ))}
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>

          {/* 快速开始 */}
          <TabPane tab="快速开始" key="quickstart">
            <Alert
              message="快速设置向导"
              description="按照以下步骤完成插件的基础配置，开始使用全渠道运营功能"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Row gutter={24}>
              <Col span={8}>
                <Card title="设置进度" size="small">
                  <Progress
                    type="circle"
                    percent={setupProgress}
                    format={() => `${currentStep + 1}/${setupSteps.length}`}
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button
                      type="primary"
                      disabled={currentStep >= setupSteps.length - 1}
                      onClick={() => setCurrentStep(currentStep + 1)}
                    >
                      下一步
                    </Button>
                  </div>
                </Card>
              </Col>
              <Col span={16}>
                <Steps
                  current={currentStep}
                  direction="vertical"
                  size="small"
                >
                  {setupSteps.map((step, index) => (
                    <Step
                      key={index}
                      title={step.title}
                      description={step.description}
                      icon={currentStep > index ? <CheckCircleOutlined /> : undefined}
                    />
                  ))}
                </Steps>
              </Col>
            </Row>

            <Divider />

            <Card title={setupSteps[currentStep]?.title} size="small">
              {setupSteps[currentStep]?.content}
            </Card>
          </TabPane>

          {/* 功能说明 */}
          <TabPane tab="功能说明" key="features">
            <Collapse defaultActiveKey={['1']}>
              <Panel header="🛍️ 智能商品管理" key="1">
                <Timeline>
                  <Timeline.Item color="blue">
                    <strong>一品多渠道同步</strong>
                    <br />
                    一个商品可以同步到多个销售渠道，支持不同渠道的个性化配置
                  </Timeline.Item>
                  <Timeline.Item color="green">
                    <strong>AI智能优化</strong>
                    <br />
                    使用AI技术优化商品标题、描述、关键词，提升搜索排名和转化率
                  </Timeline.Item>
                  <Timeline.Item color="orange">
                    <strong>批量操作</strong>
                    <br />
                    支持批量价格调整、库存更新、渠道同步等操作，提升管理效率
                  </Timeline.Item>
                </Timeline>
              </Panel>

              <Panel header="🤖 AI智能体助手" key="2">
                <Timeline>
                  <Timeline.Item color="purple">
                    <strong>对话式交互</strong>
                    <br />
                    通过自然语言与AI助手对话，执行各种运营任务
                  </Timeline.Item>
                  <Timeline.Item color="cyan">
                    <strong>智能决策</strong>
                    <br />
                    基于数据分析提供智能建议，辅助运营决策
                  </Timeline.Item>
                  <Timeline.Item color="geekblue">
                    <strong>任务自动化</strong>
                    <br />
                    自动执行重复性任务，释放人力资源
                  </Timeline.Item>
                </Timeline>
              </Panel>

              <Panel header="🎨 AI图片生成" key="3">
                <Timeline>
                  <Timeline.Item color="gold">
                    <strong>多模型支持</strong>
                    <br />
                    支持DALL-E 3、Midjourney、Stable Diffusion等多种AI模型
                  </Timeline.Item>
                  <Timeline.Item color="lime">
                    <strong>平台定制</strong>
                    <br />
                    针对不同平台的要求生成定制化图片
                  </Timeline.Item>
                  <Timeline.Item color="volcano">
                    <strong>批量生成</strong>
                    <br />
                    支持批量生成商品图片，提升工作效率
                  </Timeline.Item>
                </Timeline>
              </Panel>

              <Panel header="🔗 智能类目映射" key="4">
                <Timeline>
                  <Timeline.Item color="magenta">
                    <strong>多级类目支持</strong>
                    <br />
                    支持复杂的多级类目结构，精确映射到平台类目
                  </Timeline.Item>
                  <Timeline.Item color="red">
                    <strong>AI智能映射</strong>
                    <br />
                    使用AI技术自动建议最佳的类目映射关系
                  </Timeline.Item>
                  <Timeline.Item color="orange">
                    <strong>批量映射</strong>
                    <br />
                    支持批量创建和更新类目映射关系
                  </Timeline.Item>
                </Timeline>
              </Panel>
            </Collapse>
          </TabPane>

          {/* 使用技巧 */}
          <TabPane tab="使用技巧" key="tips">
            <Row gutter={[16, 16]}>
              {tips.map((tip, index) => (
                <Col span={12} key={index}>
                  <Card size="small">
                    <Card.Meta
                      avatar={tip.icon}
                      title={tip.title}
                      description={tip.content}
                    />
                  </Card>
                </Col>
              ))}
            </Row>

            <Divider />

            <Alert
              message="最佳实践建议"
              description={
                <ul style={{ marginBottom: 0 }}>
                  <li>定期备份重要的自动化规则和配置</li>
                  <li>监控API调用量，避免超出限额</li>
                  <li>合理设置价格监控频率，平衡实时性和成本</li>
                  <li>定期检查和更新类目映射关系</li>
                  <li>关注平台政策变化，及时调整运营策略</li>
                </ul>
              }
              type="success"
              showIcon
            />
          </TabPane>

          {/* 常见问题 */}
          <TabPane tab="常见问题" key="faq">
            <Collapse>
              {faqData.map((faq, index) => (
                <Panel
                  header={
                    <Space>
                      <QuestionCircleOutlined />
                      {faq.question}
                    </Space>
                  }
                  key={index}
                >
                  <Text>{faq.answer}</Text>
                </Panel>
              ))}
            </Collapse>

            <Divider />

            <Alert
              message="需要更多帮助？"
              description={
                <Space>
                  <Text>如果您遇到其他问题，可以：</Text>
                  <Button type="link" size="small">查看详细文档</Button>
                  <Button type="link" size="small">联系技术支持</Button>
                  <Button type="link" size="small">提交反馈</Button>
                </Space>
              }
              type="info"
              showIcon
            />
          </TabPane>

          {/* 更新日志 */}
          <TabPane tab="更新日志" key="changelog">
            <Timeline>
              <Timeline.Item color="green">
                <strong>v2.0.0</strong> - 2024-01-20
                <ul>
                  <li>新增：增强的类目映射系统</li>
                  <li>新增：平台活动管理功能</li>
                  <li>新增：可视化自动化规则编辑器</li>
                  <li>新增：AI智能竞品监控</li>
                  <li>优化：商品管理界面重构</li>
                  <li>优化：AI图片生成支持更多模型</li>
                </ul>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <strong>v1.5.0</strong> - 2024-01-10
                <ul>
                  <li>新增：AI智能体对话功能</li>
                  <li>新增：批量操作支持</li>
                  <li>优化：渠道同步性能</li>
                  <li>修复：价格监控bug</li>
                </ul>
              </Timeline.Item>
              <Timeline.Item color="gray">
                <strong>v1.0.0</strong> - 2024-01-01
                <ul>
                  <li>首次发布全渠道运营助手</li>
                  <li>支持基础的商品同步功能</li>
                  <li>支持美团、饿了么平台</li>
                </ul>
              </Timeline.Item>
            </Timeline>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PluginGuide;
