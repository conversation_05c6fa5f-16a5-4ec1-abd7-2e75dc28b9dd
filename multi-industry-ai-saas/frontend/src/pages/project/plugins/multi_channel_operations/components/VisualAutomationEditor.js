import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Typography,
  Row,
  Col,
  Divider,
  Al<PERSON>,
  <PERSON><PERSON><PERSON>,
  Badge,
  Drawer,
  Tree,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  SaveOutlined,
  DeleteOutlined,
  CopyOutlined,
  ApiOutlined,
  RobotOutlined,
  LinkOutlined
} from '@ant-design/icons';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  ConnectionLineType,
  Panel,
  ReactFlowProvider
} from 'reactflow';
import 'reactflow/dist/style.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// 自定义节点类型
const nodeTypes = {
  trigger: TriggerNode,
  condition: ConditionNode,
  action: ActionNode,
  aiAgent: AIAgentNode,
  webhook: WebhookNode,
  delay: DelayNode
};

// 触发器节点
function TriggerNode({ data, isConnectable }) {
  return (
    <div className="automation-node trigger-node">
      <div className="node-header">
        <ThunderboltOutlined style={{ color: '#52c41a' }} />
        <span>触发器</span>
      </div>
      <div className="node-content">
        <Text strong>{data.label}</Text>
        <div className="node-description">
          <Text type="secondary">{data.description}</Text>
        </div>
      </div>
      <Handle type="source" position={Position.Bottom} isConnectable={isConnectable} />
    </div>
  );
}

// 条件节点
function ConditionNode({ data, isConnectable }) {
  return (
    <div className="automation-node condition-node">
      <div className="node-header">
        <BranchesOutlined style={{ color: '#1890ff' }} />
        <span>条件判断</span>
      </div>
      <div className="node-content">
        <Text strong>{data.label}</Text>
        <div className="node-description">
          <Text type="secondary">{data.description}</Text>
        </div>
      </div>
      <Handle type="target" position={Position.Top} isConnectable={isConnectable} />
      <Handle type="source" position={Position.Bottom} id="true" isConnectable={isConnectable} />
      <Handle type="source" position={Position.Right} id="false" isConnectable={isConnectable} />
    </div>
  );
}

// 动作节点
function ActionNode({ data, isConnectable }) {
  return (
    <div className="automation-node action-node">
      <div className="node-header">
        <PlayCircleOutlined style={{ color: '#fa8c16' }} />
        <span>执行动作</span>
      </div>
      <div className="node-content">
        <Text strong>{data.label}</Text>
        <div className="node-description">
          <Text type="secondary">{data.description}</Text>
        </div>
      </div>
      <Handle type="target" position={Position.Top} isConnectable={isConnectable} />
      <Handle type="source" position={Position.Bottom} isConnectable={isConnectable} />
    </div>
  );
}

// AI智能体节点
function AIAgentNode({ data, isConnectable }) {
  return (
    <div className="automation-node ai-agent-node">
      <div className="node-header">
        <RobotOutlined style={{ color: '#722ed1' }} />
        <span>AI智能体</span>
      </div>
      <div className="node-content">
        <Text strong>{data.label}</Text>
        <div className="node-description">
          <Text type="secondary">{data.description}</Text>
        </div>
      </div>
      <Handle type="target" position={Position.Top} isConnectable={isConnectable} />
      <Handle type="source" position={Position.Bottom} isConnectable={isConnectable} />
    </div>
  );
}

// Webhook节点
function WebhookNode({ data, isConnectable }) {
  return (
    <div className="automation-node webhook-node">
      <div className="node-header">
        <ApiOutlined style={{ color: '#13c2c2' }} />
        <span>Webhook</span>
      </div>
      <div className="node-content">
        <Text strong>{data.label}</Text>
        <div className="node-description">
          <Text type="secondary">{data.description}</Text>
        </div>
      </div>
      <Handle type="target" position={Position.Top} isConnectable={isConnectable} />
      <Handle type="source" position={Position.Bottom} isConnectable={isConnectable} />
    </div>
  );
}

// 延迟节点
function DelayNode({ data, isConnectable }) {
  return (
    <div className="automation-node delay-node">
      <div className="node-header">
        <PauseCircleOutlined style={{ color: '#faad14' }} />
        <span>延迟等待</span>
      </div>
      <div className="node-content">
        <Text strong>{data.label}</Text>
        <div className="node-description">
          <Text type="secondary">{data.description}</Text>
        </div>
      </div>
      <Handle type="target" position={Position.Top} isConnectable={isConnectable} />
      <Handle type="source" position={Position.Bottom} isConnectable={isConnectable} />
    </div>
  );
}

const VisualAutomationEditor = ({ projectId }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [nodeConfigVisible, setNodeConfigVisible] = useState(false);
  const [ruleTemplateVisible, setRuleTemplateVisible] = useState(false);
  const [saveRuleVisible, setSaveRuleVisible] = useState(false);
  const [form] = Form.useForm();

  // 节点模板
  const nodeTemplates = [
    {
      type: 'trigger',
      category: '触发器',
      items: [
        {
          id: 'price_change',
          label: '价格变化',
          description: '当商品价格发生变化时触发',
          config: {
            threshold: 5,
            comparison: 'percentage'
          }
        },
        {
          id: 'competitor_price',
          label: '竞品价格变化',
          description: '当竞品价格变化时触发',
          config: {
            competitors: [],
            threshold: 10
          }
        },
        {
          id: 'stock_low',
          label: '库存不足',
          description: '当库存低于阈值时触发',
          config: {
            threshold: 10
          }
        },
        {
          id: 'time_schedule',
          label: '定时触发',
          description: '按照时间计划触发',
          config: {
            schedule: 'daily',
            time: '09:00'
          }
        }
      ]
    },
    {
      type: 'condition',
      category: '条件判断',
      items: [
        {
          id: 'price_comparison',
          label: '价格比较',
          description: '比较价格是否满足条件',
          config: {
            operator: 'greater_than',
            value: 0
          }
        },
        {
          id: 'time_range',
          label: '时间范围',
          description: '检查当前时间是否在指定范围内',
          config: {
            start_time: '09:00',
            end_time: '18:00'
          }
        },
        {
          id: 'channel_status',
          label: '渠道状态',
          description: '检查渠道是否在线',
          config: {
            channels: []
          }
        }
      ]
    },
    {
      type: 'action',
      category: '执行动作',
      items: [
        {
          id: 'update_price',
          label: '更新价格',
          description: '自动调整商品价格',
          config: {
            strategy: 'follow_competitor',
            margin: 5
          }
        },
        {
          id: 'sync_product',
          label: '同步商品',
          description: '将商品同步到指定渠道',
          config: {
            channels: [],
            sync_options: ['price', 'stock']
          }
        },
        {
          id: 'send_notification',
          label: '发送通知',
          description: '发送通知消息',
          config: {
            type: 'email',
            recipients: []
          }
        },
        {
          id: 'update_stock',
          label: '更新库存',
          description: '自动调整库存数量',
          config: {
            action: 'increase',
            quantity: 10
          }
        }
      ]
    },
    {
      type: 'aiAgent',
      category: 'AI智能体',
      items: [
        {
          id: 'ai_price_optimize',
          label: 'AI价格优化',
          description: '使用AI分析并优化价格',
          config: {
            model: 'gpt-4',
            factors: ['competitor_price', 'demand', 'inventory']
          }
        },
        {
          id: 'ai_content_generate',
          label: 'AI内容生成',
          description: '生成商品描述或标题',
          config: {
            content_type: 'description',
            style: 'professional'
          }
        },
        {
          id: 'ai_decision_maker',
          label: 'AI决策助手',
          description: '基于数据做出智能决策',
          config: {
            decision_type: 'pricing',
            confidence_threshold: 0.8
          }
        }
      ]
    },
    {
      type: 'webhook',
      category: '外部集成',
      items: [
        {
          id: 'webhook_call',
          label: 'Webhook调用',
          description: '调用外部API接口',
          config: {
            url: '',
            method: 'POST',
            headers: {}
          }
        },
        {
          id: 'n8n_workflow',
          label: 'N8N工作流',
          description: '触发N8N工作流',
          config: {
            workflow_id: '',
            webhook_url: ''
          }
        }
      ]
    }
  ];

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = (event, node) => {
    setSelectedNode(node);
    setNodeConfigVisible(true);
  };

  const addNode = (template) => {
    const newNode = {
      id: `${template.type}_${Date.now()}`,
      type: template.type,
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: {
        label: template.label,
        description: template.description,
        config: template.config
      }
    };

    setNodes((nds) => nds.concat(newNode));
  };

  const deleteNode = (nodeId) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
  };

  const saveRule = async (values) => {
    try {
      const ruleData = {
        name: values.name,
        description: values.description,
        is_active: values.is_active,
        nodes: nodes,
        edges: edges,
        config: values
      };

      // 调用API保存规则
      // await automationService.saveRule(projectId, ruleData);
      
      message.success('自动化规则保存成功');
      setSaveRuleVisible(false);
    } catch (error) {
      message.error('保存失败');
    }
  };

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <Card size="small" style={{ marginBottom: 8 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setRuleTemplateVisible(true)}
              >
                添加节点
              </Button>
              <Button
                icon={<SaveOutlined />}
                onClick={() => setSaveRuleVisible(true)}
              >
                保存规则
              </Button>
              <Button icon={<EyeOutlined />}>
                预览
              </Button>
              <Button icon={<PlayCircleOutlined />}>
                测试运行
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Text type="secondary">节点: {nodes.length}</Text>
              <Text type="secondary">连接: {edges.length}</Text>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 流程编辑器 */}
      <div style={{ flex: 1, border: '1px solid #d9d9d9', borderRadius: 6 }}>
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            connectionLineType={ConnectionLineType.SmoothStep}
            fitView
          >
            <Controls />
            <MiniMap />
            <Background variant="dots" gap={12} size={1} />
            <Panel position="top-right">
              <Card size="small">
                <Space direction="vertical" size="small">
                  <Text strong>快捷操作</Text>
                  <Button size="small" block>清空画布</Button>
                  <Button size="small" block>导入模板</Button>
                  <Button size="small" block>导出配置</Button>
                </Space>
              </Card>
            </Panel>
          </ReactFlow>
        </ReactFlowProvider>
      </div>

      {/* 节点模板抽屉 */}
      <Drawer
        title="添加节点"
        placement="right"
        width={400}
        open={ruleTemplateVisible}
        onClose={() => setRuleTemplateVisible(false)}
      >
        <Tabs defaultActiveKey="templates">
          <TabPane tab="节点模板" key="templates">
            {nodeTemplates.map((category) => (
              <div key={category.type} style={{ marginBottom: 24 }}>
                <Title level={5}>{category.category}</Title>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {category.items.map((template) => (
                    <Card
                      key={template.id}
                      size="small"
                      hoverable
                      onClick={() => addNode(template)}
                      style={{ cursor: 'pointer' }}
                    >
                      <Card.Meta
                        title={template.label}
                        description={template.description}
                      />
                    </Card>
                  ))}
                </Space>
              </div>
            ))}
          </TabPane>
          <TabPane tab="AI组件" key="ai">
            <Alert
              message="AI增强功能"
              description="集成AI能力，让自动化规则更智能"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            {/* AI组件列表 */}
          </TabPane>
          <TabPane tab="第三方集成" key="integrations">
            <Alert
              message="第三方集成"
              description="连接外部系统和服务"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            {/* 第三方集成组件 */}
          </TabPane>
        </Tabs>
      </Drawer>

      {/* 节点配置模态框 */}
      <Modal
        title={`配置节点: ${selectedNode?.data?.label}`}
        open={nodeConfigVisible}
        onCancel={() => setNodeConfigVisible(false)}
        footer={null}
        width={600}
      >
        {selectedNode && (
          <NodeConfigForm
            node={selectedNode}
            onSave={(config) => {
              // 更新节点配置
              setNodes((nds) =>
                nds.map((node) =>
                  node.id === selectedNode.id
                    ? { ...node, data: { ...node.data, config } }
                    : node
                )
              );
              setNodeConfigVisible(false);
            }}
            onDelete={() => {
              deleteNode(selectedNode.id);
              setNodeConfigVisible(false);
            }}
          />
        )}
      </Modal>

      {/* 保存规则模态框 */}
      <Modal
        title="保存自动化规则"
        open={saveRuleVisible}
        onCancel={() => setSaveRuleVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveRule}
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="输入规则名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="规则描述"
          >
            <Input.TextArea rows={3} placeholder="描述规则的作用和触发条件" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用规则"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setSaveRuleVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      <style jsx>{`
        .automation-node {
          background: white;
          border: 2px solid #d9d9d9;
          border-radius: 8px;
          padding: 12px;
          min-width: 180px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .automation-node:hover {
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .node-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .node-content {
          text-align: center;
        }

        .node-description {
          margin-top: 4px;
          font-size: 12px;
        }

        .trigger-node {
          border-color: #52c41a;
        }

        .condition-node {
          border-color: #1890ff;
        }

        .action-node {
          border-color: #fa8c16;
        }

        .ai-agent-node {
          border-color: #722ed1;
        }

        .webhook-node {
          border-color: #13c2c2;
        }

        .delay-node {
          border-color: #faad14;
        }
      `}</style>
    </div>
  );
};

// 节点配置表单组件
const NodeConfigForm = ({ node, onSave, onDelete }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(node.data.config);
  }, [node, form]);

  const handleSave = (values) => {
    onSave(values);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSave}
    >
      {/* 根据节点类型渲染不同的配置表单 */}
      {node.type === 'trigger' && (
        <>
          <Form.Item name="threshold" label="触发阈值">
            <Input placeholder="输入触发阈值" />
          </Form.Item>
          <Form.Item name="comparison" label="比较方式">
            <Select>
              <Option value="percentage">百分比</Option>
              <Option value="absolute">绝对值</Option>
            </Select>
          </Form.Item>
        </>
      )}

      {node.type === 'action' && (
        <>
          <Form.Item name="strategy" label="执行策略">
            <Select>
              <Option value="immediate">立即执行</Option>
              <Option value="delayed">延迟执行</Option>
              <Option value="conditional">条件执行</Option>
            </Select>
          </Form.Item>
        </>
      )}

      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Space>
          <Button danger onClick={onDelete}>
            删除节点
          </Button>
          <Button type="primary" htmlType="submit">
            保存配置
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default VisualAutomationEditor;
