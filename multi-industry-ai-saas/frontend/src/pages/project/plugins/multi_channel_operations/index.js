import React, { useState, useEffect } from 'react';
import { Card, Tabs, Spin, message } from 'antd';
import {
  DashboardOutlined,
  ShopOutlined,
  ShoppingOutlined,
  DollarOutlined,
  EyeOutlined,
  RobotOutlined,
  BarChartOutlined,
  SettingOutlined,
  PictureOutlined,
  LinkOutlined,
  GiftOutlined,
  BookOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useParams, useNavigate, useLocation } from 'react-router-dom';

// 导入子页面组件
import Dashboard from './components/Dashboard';
import ChannelManagement from './components/ChannelManagement';
import ProductManagement from './components/ProductManagementNew';
import PricingStrategy from './components/PricingStrategy';
import CompetitorMonitoring from './components/CompetitorMonitoring';
import AutomationRules from './components/AutomationRules';
import Analytics from './components/Analytics';
import Settings from './components/Settings';
import AIAgent from './components/AIAgent';
import AIImageGenerator from './components/AIImageGenerator';
import CategoryMapping from './components/CategoryMapping';
import PlatformActivities from './components/PlatformActivities';
import VisualAutomationEditor from './components/VisualAutomationEditor';
import PluginGuide from './components/PluginGuide';

const { TabPane } = Tabs;

const MultiChannelOperations = () => {
  const { projectId, tab } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');

  // 标签页配置
  const tabItems = [
    {
      key: 'dashboard',
      tab: (
        <span>
          <DashboardOutlined />
          运营总览
        </span>
      ),
      component: Dashboard
    },
    {
      key: 'channels',
      tab: (
        <span>
          <ShopOutlined />
          渠道管理
        </span>
      ),
      component: ChannelManagement
    },
    {
      key: 'products',
      tab: (
        <span>
          <ShoppingOutlined />
          商品管理
        </span>
      ),
      component: ProductManagement
    },
    {
      key: 'pricing',
      tab: (
        <span>
          <DollarOutlined />
          定价策略
        </span>
      ),
      component: PricingStrategy
    },
    {
      key: 'competitors',
      tab: (
        <span>
          <EyeOutlined />
          竞品监控
        </span>
      ),
      component: CompetitorMonitoring
    },
    {
      key: 'automation',
      tab: (
        <span>
          <ThunderboltOutlined />
          自动化规则
        </span>
      ),
      component: VisualAutomationEditor
    },
    {
      key: 'analytics',
      tab: (
        <span>
          <BarChartOutlined />
          数据分析
        </span>
      ),
      component: Analytics
    },
    {
      key: 'ai-agent',
      tab: (
        <span>
          <RobotOutlined />
          AI智能体
        </span>
      ),
      component: AIAgent
    },
    {
      key: 'ai-image',
      tab: (
        <span>
          <PictureOutlined />
          AI图片生成
        </span>
      ),
      component: AIImageGenerator
    },
    {
      key: 'category-mapping',
      tab: (
        <span>
          <LinkOutlined />
          类目映射
        </span>
      ),
      component: CategoryMapping
    },
    {
      key: 'platform-activities',
      tab: (
        <span>
          <GiftOutlined />
          平台活动
        </span>
      ),
      component: PlatformActivities
    },
    {
      key: 'guide',
      tab: (
        <span>
          <BookOutlined />
          插件说明
        </span>
      ),
      component: PluginGuide
    },
    {
      key: 'settings',
      tab: (
        <span>
          <SettingOutlined />
          插件设置
        </span>
      ),
      component: Settings
    }
  ];

  // 当URL变化时，更新activeTab
  useEffect(() => {
    const validTabs = tabItems.map(item => item.key);

    // 优先使用URL参数中的tab
    if (tab && validTabs.includes(tab)) {
      setActiveTab(tab);
    } else {
      // 如果没有tab参数，从路径中解析
      const pathParts = location.pathname.split('/');
      const currentTab = pathParts[pathParts.length - 1];

      if (validTabs.includes(currentTab)) {
        setActiveTab(currentTab);
      } else if (location.pathname.includes('multi-channel-operations')) {
        setActiveTab('dashboard');
      }
    }
  }, [tab, location.pathname, tabItems]);

  const handleTabChange = (key) => {
    // 更新activeTab状态
    setActiveTab(key);
    // 使用navigate进行路由跳转，确保URL正确更新
    const newPath = `/project/${projectId}/plugins/multi-channel-operations/${key}`;
    navigate(newPath, { replace: true });
  };

  const getComponentForTab = (tabKey) => {
    const currentItem = tabItems.find(item => item.key === tabKey);
    if (currentItem) {
      const Component = currentItem.component;
      return <Component projectId={projectId} />;
    }
    return <Dashboard projectId={projectId} />; // 默认显示Dashboard
  };

  return (
    <div style={{ padding: '0', background: '#f0f2f5', minHeight: 'calc(100vh - 64px)' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: 20 }}>🛒</span>
            <span>全渠道运营助手</span>
          </div>
        }
        style={{ margin: 0 }}
        bodyStyle={{ padding: 0 }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          type="card"
          style={{ margin: 0 }}
          tabBarStyle={{
            margin: 0,
            paddingLeft: 16,
            paddingRight: 16,
            background: '#fafafa'
          }}
        >
          {tabItems.map(item => (
            <TabPane tab={item.tab} key={item.key}>
              <div style={{ padding: '24px' }}>
                <Spin spinning={loading} tip="加载中...">
                  {getComponentForTab(item.key)}
                </Spin>
              </div>
            </TabPane>
          ))}
        </Tabs>
      </Card>
    </div>
  );
};

export default MultiChannelOperations;
