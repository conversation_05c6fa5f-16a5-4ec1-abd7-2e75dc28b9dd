import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Tabs,
  Row,
  Col,
  Statistic,
  DatePicker,
  Spin,
  Empty
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, <PERSON><PERSON><PERSON>Outlined, <PERSON><PERSON><PERSON>Outlined, Pie<PERSON><PERSON>Outlined, AppstoreOutlined } from '@ant-design/icons';
import apiService from '../../../services/api';
import { Column, Pie, Bar } from '@ant-design/plots';
import dayjs from 'dayjs';
import ErrorBoundary from '../../../components/common/ErrorBoundary';

const { Option } = Select;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

// 渠道概览组件
const ChannelOverview = () => {
  const [loading, setLoading] = useState(true);
  const [overviewData, setOverviewData] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(29, 'days'), dayjs()]);
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState('all');

  const fetchOverview = async (start, end, storeId) => {
    setLoading(true);
    try {
      const params = {
        start_date: start.format('YYYY-MM-DD'),
        end_date: end.format('YYYY-MM-DD'),
        store_id: storeId === 'all' ? null : storeId,
      };
      const data = await apiService.project.salesManagement.getOverview(params);
      
      // Data integrity check
      if (typeof data === 'object' && data !== null && Array.isArray(data.platform_sales) && Array.isArray(data.daily_trend)) {
        setOverviewData(data);
      } else {
        console.error("Received malformed overview data:", data);
        message.error('渠道概览数据格式错误，无法渲染图表');
        setOverviewData(null); // Set to null to show Empty state
      }

    } catch (error) {
      message.error('加载渠道概览数据失败');
      console.error("加载渠道概览数据失败:", error);
      setOverviewData(null); // Also clear on API failure
    } finally {
      setLoading(false);
    }
  };

  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      setStores(response.items || []);
    } catch (error) {
      message.error('加载门店列表失败');
    }
  };

  useEffect(() => {
    fetchStores();
  }, []);
  
  useEffect(() => {
    if (dateRange.length === 2) {
      fetchOverview(dateRange[0], dateRange[1], selectedStore);
    }
  }, [dateRange, selectedStore]);
  
  const handleDateChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  const handleStoreChange = (storeId) => {
    setSelectedStore(storeId);
  };

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" /></div>;
  }
  
  if (!overviewData) {
    return <Empty description="暂无渠道数据" />;
  }

  const platformSalesConfig = {
    data: overviewData?.platform_sales || [],
    angleField: 'sales',
    colorField: 'platform_name',
    radius: 0.8,
    label: {
      type: 'spider',
      labelHeight: 28,
      formatter: (datum) => `${datum.platform_name}\n¥${datum.sales.toFixed(2)}`,
    },
    interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
  };
  
  const dailyTrendConfig = {
    data: overviewData?.daily_trend || [],
    xField: 'date',
    yField: 'sales',
    xAxis: {
      tickCount: 5,
    },
    smooth: true,
  };

  const barChartConfig = (data, xField, yField, title) => ({
    data: data || [],
    xField,
    yField,
    seriesField: yField,
    legend: { position: 'top-left' },
    yAxis: {
        label: {
            autoHide: true,
            autoRotate: false,
        },
    },
    tooltip: {
        formatter: (datum) => ({ name: title, value: `¥${datum[xField].toFixed(2)}` }),
    },
  });

  const pieChartConfig = (data, angleField, colorField) => ({
      appendPadding: 10,
      data: data || [],
      angleField,
      colorField,
      radius: 0.8,
      label: {
        type: 'inner',
        offset: '-50%',
        content: ({ percent }) => `${(percent * 100).toFixed(0)}%`,
        style: {
          textAlign: 'center',
          fontSize: 14,
        },
      },
      interactions: [{ type: 'element-active' }],
      tooltip: {
        formatter: (datum) => ({ name: datum[colorField], value: `¥${datum[angleField].toFixed(2)}` }),
      },
  })

  const renderGlobalView = () => (
    <>
        <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
                <Card title="门店销售额排行 (Top 10)">
                    {overviewData.top_stores_by_sales?.length > 0 ? 
                     <Bar {...barChartConfig(overviewData.top_stores_by_sales, 'sales', 'name', '销售额')} height={300}/>
                     : <Empty description="暂无门店排行数据"/>}
                </Card>
            </Col>
            <Col span={12}>
                <Card title="渠道实例销售排行">
                     {overviewData.top_channel_instances_by_sales?.length > 0 ? 
                     <Bar {...barChartConfig(overviewData.top_channel_instances_by_sales, 'sales', 'name', '销售额')} height={300}/>
                     : <Empty description="暂无渠道实例排行数据"/>}
                </Card>
            </Col>
        </Row>
        <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
                <Card title="支付方式分布">
                     {overviewData.payment_method_summary?.length > 0 ?
                     <Pie {...pieChartConfig(overviewData.payment_method_summary, 'amount', 'name')} height={300}/>
                     : <Empty description="暂无支付方式数据"/>}
                </Card>
            </Col>
        </Row>
    </>
  );

  const renderStoreView = () => (
    <>
        <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
                <Card title="各渠道实例销售分析">
                    {overviewData.channel_instance_summary?.length > 0 ? 
                    <Bar {...barChartConfig(overviewData.channel_instance_summary, 'sales', 'name', '销售额')} height={300}/>
                    : <Empty description="暂无渠道实例数据"/>}
                </Card>
            </Col>
            <Col span={12}>
                <Card title="支付方式分布">
                    {overviewData.payment_method_summary?.length > 0 ?
                    <Pie {...pieChartConfig(overviewData.payment_method_summary, 'amount', 'name')} height={300}/>
                    : <Empty description="暂无支付方式数据"/>}
                </Card>
            </Col>
        </Row>
    </>
  );


  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Space>
        <RangePicker value={dateRange} onChange={handleDateChange} />
        <Select value={selectedStore} onChange={handleStoreChange} style={{ width: 200 }}>
          <Option value="all">全部门店</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Space>
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic title="总销售额 (选定周期)" value={overviewData.total_sales} prefix="¥" precision={2} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总订单数 (选定周期)" value={overviewData.total_orders} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="渠道实例总数" value={overviewData.total_channels} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="当前活跃实例" value={overviewData.active_channels} />
          </Card>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Card title="各平台销售分布">
            <ErrorBoundary>
              <Pie {...platformSalesConfig} height={300} />
            </ErrorBoundary>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="销售趋势">
            <ErrorBoundary>
             <Column {...dailyTrendConfig} height={300} />
            </ErrorBoundary>
          </Card>
        </Col>
      </Row>
      {/* Conditionally render new charts */}
      {selectedStore === 'all' ? renderGlobalView() : renderStoreView()}
    </Space>
  );
};


// 渠道实例管理组件
const ChannelInstanceManagement = () => {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState(null);
  const [form] = Form.useForm();
  
  // New state for template based creation
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [stores, setStores] = useState([]);
  const [platforms, setPlatforms] = useState([]);
  const [selectedStore, setSelectedStore] = useState(null);
  const [templates, setTemplates] = useState([]);

  // 基础数据
  const [services, setServices] = useState([]);
  const [businessModes, setBusinessModes] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);

  const fetchChannels = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.salesManagement.getChannels();
      setChannels(response.items);
    } catch (error) {
      message.error('加载渠道列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchPrerequisites = async () => {
    try {
      const [storesRes, servicesRes, modesRes, paymentsRes, templatesRes] = await Promise.all([
        apiService.project.store.getList(),
        apiService.project.salesManagement.getAllServices(),
        apiService.project.salesManagement.getBusinessModes(),
        apiService.project.salesManagement.getPaymentMethods(),
        apiService.project.salesManagement.getTemplates(),
      ]);
      setStores(storesRes.items || []);
      setServices(servicesRes || []);
      setBusinessModes(modesRes || []);
      setPaymentMethods(paymentsRes.items || []);
      setTemplates(templatesRes.items || []);
    } catch (error) {
      message.error('加载基础数据失败');
    }
  };

  useEffect(() => {
    fetchChannels();
    fetchPrerequisites();
  }, []);
  
  const handleCreateFromTemplate = () => {
    setTemplateModalVisible(true);
  };

  const handleTemplateSubmit = async (values) => {
    const { template_id, store_ids } = values;
    if (!template_id || !store_ids || store_ids.length === 0) {
      message.warn('请选择一个模板和至少一个门店');
      return;
    }

    setLoading(true);
    try {
      await apiService.project.salesManagement.batchCreateChannels({ template_id, store_ids });
      message.success('渠道实例批量创建成功!');
      setTemplateModalVisible(false);
      fetchChannels();
    } catch (error) {
      message.error('批量创建失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };


  const handleEdit = (record) => {
    setEditingChannel(record);
    const fieldsToSet = {
      ...record,
      store_id: record.store ? record.store.id : record.store_id,
      service_id: record.service?.id,
      business_mode_id: record.business_mode?.id,
      payment_method_ids: record.payment_methods?.map(pm => pm.id) || [],
    };
    form.setFieldsValue(fieldsToSet);
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await apiService.project.salesManagement.deleteChannel(id);
      message.success('删除成功');
      fetchChannels();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingChannel) {
        await apiService.project.salesManagement.updateChannel(editingChannel.id, values);
        message.success('更新成功');
      } else {
        await apiService.project.salesManagement.createChannel(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      setEditingChannel(null);
      fetchChannels();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const columns = [
    { title: '渠道实例名称', dataIndex: 'custom_name', key: 'custom_name' },
    { title: '所属门店', dataIndex: ['store', 'name'], key: 'store_name' },
    { title: '渠道平台', dataIndex: ['service', 'platform', 'name'], key: 'platform_name' },
    { title: '业务模式', dataIndex: ['business_mode', 'name'], key: 'business_mode' },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (isActive ? '激活' : '禁用'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Space style={{ marginBottom: 16 }}>
        <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
                setEditingChannel(null);
                form.resetFields();
                setModalVisible(true);
            }}
        >
            手动创建渠道实例
        </Button>
        <Button
            icon={<AppstoreOutlined />}
            onClick={handleCreateFromTemplate}
        >
            从模板批量创建
        </Button>
      </Space>
      <Table columns={columns} dataSource={channels} loading={loading} rowKey="id" />

      {/* Manual Create/Edit Modal */}
      <Modal
        title={editingChannel ? '编辑渠道实例' : '创建渠道实例'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        destroyOnClose
      >
        <Form form={form} layout="vertical" name="channel_form">
          <Form.Item name="store_id" label="所属门店" rules={[{ required: true }]}>
            <Select placeholder="选择门店">
              {stores.map(store => <Option key={store.id} value={store.id}>{store.name}</Option>)}
            </Select>
          </Form.Item>
          <Form.Item name="custom_name" label="渠道实例名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="service_id" label="平台服务" rules={[{ required: true }]}>
             <Select placeholder="选择平台服务">
              {services.map(service => <Option key={service.id} value={service.id}>{service.name} ({service.platform.name})</Option>)}
            </Select>
          </Form.Item>
          <Form.Item name="business_mode_id" label="业务模式">
             <Select placeholder="选择业务模式">
              {businessModes.map(mode => <Option key={mode.id} value={mode.id}>{mode.name}</Option>)}
            </Select>
          </Form.Item>
           <Form.Item name="payment_method_ids" label="支持的支付方式">
            <Select mode="multiple" placeholder="选择支付方式">
              {paymentMethods.map(pm => <Option key={pm.id} value={pm.id}>{pm.name}</Option>)}
            </Select>
          </Form.Item>
          <Form.Item name="is_active" label="状态" valuePropName="checked">
            <Switch />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <Input.TextArea rows={2} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Template Create Modal */}
      <Modal
        title="从模板批量创建渠道实例"
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <Form onFinish={handleTemplateSubmit} layout="vertical">
          <Form.Item name="template_id" label="选择实例模板" rules={[{ required: true }]}>
            <Select placeholder="请选择一个模板">
              {templates.map(t => <Option key={t.id} value={t.id}>{t.name}</Option>)}
            </Select>
          </Form.Item>
          <Form.Item name="store_ids" label="目标门店" rules={[{ required: true }]}>
            <Select mode="multiple" placeholder="请选择一个或多个门店">
              {stores.map(s => <Option key={s.id} value={s.id}>{s.name}</Option>)}
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>开始创建</Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

const ChannelManagement = () => {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab={<span><LineChartOutlined /> 渠道概览</span>} key="1">
          <ChannelOverview />
        </TabPane>
        <TabPane tab={<span><AppstoreOutlined /> 渠道实例管理</span>} key="2">
          <ChannelInstanceManagement />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default ChannelManagement;
