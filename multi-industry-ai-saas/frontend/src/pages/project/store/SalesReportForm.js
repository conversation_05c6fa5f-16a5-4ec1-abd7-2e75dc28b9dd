import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Space,
  Table,
  InputNumber,
  Typography,
  Divider,
  message,
  Row,
  Col,
  Tabs,
  Statistic,
  Spin,
  Empty
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  ArrowLeftOutlined,
  ShopOutlined,
  CreditCardOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

/**
 * 销售上报表单组件 - 门店维度
 * 主要上报每日各渠道销售额和充值/售卡金额
 * 每个销售渠道可以有多种支付方式
 */
const SalesReportForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = !!id;

  // 状态管理
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditing);
  const [stores, setStores] = useState([]);
  const [selectedStoreId, setSelectedStoreId] = useState(null);

  // 新的状态来管理渠道实例和支付方式的输入
  const [channelInstances, setChannelInstances] = useState([]);
  const [rechargePaymentMethods, setRechargePaymentMethods] = useState([]);
  const [salesData, setSalesData] = useState({}); // { channel_instance_id: { payment_method_id: { amount: 100, orders: 10 } } }
  const [rechargeData, setRechargeData] = useState({}); // { payment_method_id: { amount: 100, orders: 10 } }

  const [activeTab, setActiveTab] = useState('sales');

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      const storeItems = response?.data?.items || [];
      if (Array.isArray(storeItems)) {
        setStores(storeItems);
        if (!isEditing && storeItems.length > 0) {
          form.setFieldsValue({ store_id: storeItems[0].id });
          setSelectedStoreId(storeItems[0].id);
        }
      } else {
        console.error("获取的门店数据不是一个数组:", storeItems);
        message.error('返回的门店数据格式不正确');
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取门店的渠道实例和支付方式
  const fetchStoreConfig = async (storeId) => {
    if (!storeId) return;
    setLoading(true);
    try {
      // 获取渠道实例
      const channelsRes = await apiService.project.salesManagement.getChannels({ store_id: storeId });
      if (channelsRes && channelsRes.items) {
        // 过滤出有支付方式的渠道实例
        const instancesWithOptions = channelsRes.items.filter(inst => inst.payment_methods && inst.payment_methods.length > 0);
        setChannelInstances(instancesWithOptions);
      } else {
        setChannelInstances([]);
      }

      // 获取通用支付方式用于充值/售卡
      const paymentMethodsRes = await apiService.project.salesManagement.getPaymentMethods({ is_active: true, for_recharge: true });
      if (paymentMethodsRes && paymentMethodsRes.items) {
        setRechargePaymentMethods(paymentMethodsRes.items);
      } else {
        setRechargePaymentMethods([]);
      }
      
    } catch (error) {
      console.error('获取门店配置失败:', error);
      message.error('获取门店配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取销售上报详情
  const fetchReportDetail = async () => {
    if (!isEditing) return;

    setInitialLoading(true);
    try {
      const response = await apiService.project.salesReport.getDetail(id);
      if (response) {
        form.setFieldsValue({
          store_id: response.store_id,
          report_date: response.report_date ? dayjs(response.report_date) : dayjs(),
          report_type: response.report_type || 'daily',
          notes: response.notes
        });
        setSelectedStoreId(response.store_id);

        // 初始化已有的销售数据
        const initialSalesData = {};
        response.channel_details?.forEach(detail => {
          initialSalesData[detail.sales_channel_id] = {
            total_sales: detail.total_sales,
            total_orders: detail.total_orders,
            payment_methods_details: detail.payment_methods_details?.reduce((acc, pm) => {
              acc[pm.payment_method_id] = { amount: pm.amount, orders: pm.orders };
              return acc;
            }, {}) || {}
          };
        });
        setSalesData(initialSalesData);

        // 初始化已有的充值数据
        const initialRechargeData = {};
        response.recharge_sales_data?.forEach(pm => {
          initialRechargeData[pm.payment_method_id] = { amount: pm.amount, orders: pm.orders };
        });
        setRechargeData(initialRechargeData);

      } else {
        message.error('获取销售上报详情失败');
        navigate('/project/store/sales-report');
      }
    } catch (error) {
      console.error('获取销售上报详情失败:', error);
      message.error('获取销售上报详情失败: ' + (error.response?.data?.detail || error.message));
      navigate('/project/store/sales-report');
    } finally {
      setInitialLoading(false);
    }
  };

  // 处理销售数据输入
  const handleSalesDataChange = (channelInstanceId, paymentMethodId, field, value) => {
    setSalesData(prev => {
      const newData = { ...prev };
      if (!newData[channelInstanceId]) {
        newData[channelInstanceId] = { payment_methods_details: {} };
      }
      if (!newData[channelInstanceId].payment_methods_details[paymentMethodId]) {
        newData[channelInstanceId].payment_methods_details[paymentMethodId] = {};
      }
      newData[channelInstanceId].payment_methods_details[paymentMethodId][field] = value;
      return newData;
    });
  };
  
  // 处理充值数据输入
  const handleRechargeDataChange = (paymentMethodId, field, value) => {
    setRechargeData(prev => ({
      ...prev,
      [paymentMethodId]: {
        ...prev[paymentMethodId],
        [field]: value,
      },
    }));
  };

  const calculateTotal = (dataObject) => {
    let totalAmount = 0;
    let totalOrders = 0;
    
    if (dataObject) {
      Object.values(dataObject).forEach(channel => {
        if (channel.payment_methods_details) {
          Object.values(channel.payment_methods_details).forEach(pm => {
            totalAmount += pm.amount || 0;
            totalOrders += pm.orders || 0;
          });
        }
      });
    }
    return { totalAmount, totalOrders };
  };
  
  const calculateRechargeTotal = () => {
    let totalAmount = 0;
    let totalOrders = 0;
    if (rechargeData) {
      Object.values(rechargeData).forEach(pm => {
        totalAmount += pm.amount || 0;
        totalOrders += pm.orders || 0;
      });
    }
    return { totalAmount, totalOrders };
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const channelDetails = Object.entries(salesData).map(([channelInstanceId, data]) => {
        const paymentMethodsDetails = Object.entries(data.payment_methods_details || {}).map(([pmId, pmData]) => ({
          payment_method_id: pmId,
          amount: pmData.amount || 0,
          orders: pmData.orders || 0,
        }));
        
        const totalSales = paymentMethodsDetails.reduce((sum, pm) => sum + pm.amount, 0);
        const totalOrders = paymentMethodsDetails.reduce((sum, pm) => sum + pm.orders, 0);

        return {
          sales_channel_id: channelInstanceId,
          total_sales: totalSales,
          total_orders: totalOrders,
          payment_methods_details: paymentMethodsDetails,
        };
      });

      const rechargeSalesData = Object.entries(rechargeData).map(([pmId, pmData]) => ({
        payment_method_id: pmId,
        amount: pmData.amount || 0,
        orders: pmData.orders || 0,
      }));

      const payload = {
        ...values,
        report_date: values.report_date.toISOString(),
        channel_details: channelDetails,
        recharge_sales_data: rechargeSalesData,
      };

      if (isEditing) {
        await apiService.project.salesReport.update(id, payload);
        message.success('销售上报更新成功');
      } else {
        await apiService.project.salesReport.create(payload);
        message.success('销售上报创建成功');
      }
      navigate('/project/store/sales-report');
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/project/store/sales-report');
  };

  // --- 总计 ---
  const { totalAmount: totalSalesAmount, totalOrders: totalSalesOrders } = calculateTotal(salesData);
  const { totalAmount: totalRechargeAmount, totalOrders: totalRechargeOrders } = calculateRechargeTotal();

  const salesSection = (
    <Spin spinning={loading}>
      {channelInstances.length === 0 && !loading ? (
        <Empty description="该门店未配置任何销售渠道实例，或渠道实例未关联任何支付方式。请先在销售管理中配置。" />
      ) : (
        channelInstances.map(instance => (
          <Card 
            key={instance.id} 
            title={<><ShopOutlined /> {instance.name}</>} 
            style={{ marginBottom: 16 }}
            size="small"
          >
            {instance.payment_methods.map(pm => (
              <Row key={pm.id} gutter={16} style={{ marginBottom: 8 }} align="middle">
                <Col span={6}>
                  <Text>{pm.name}</Text>
                </Col>
                <Col span={9}>
                  <InputNumber
                    addonBefore="金额"
                    style={{ width: '100%' }}
                    value={salesData[instance.id]?.payment_methods_details?.[pm.id]?.amount}
                    onChange={(value) => handleSalesDataChange(instance.id, pm.id, 'amount', value)}
                    placeholder="请输入销售额"
                  />
                </Col>
                <Col span={9}>
                  <InputNumber
                    addonBefore="笔数"
                    style={{ width: '100%' }}
                    value={salesData[instance.id]?.payment_methods_details?.[pm.id]?.orders}
                    onChange={(value) => handleSalesDataChange(instance.id, pm.id, 'orders', value)}
                    placeholder="请输入订单数"
                  />
                </Col>
              </Row>
            ))}
          </Card>
        ))
      )}
    </Spin>
  );

  const rechargeSection = (
     <Spin spinning={loading}>
      <Card title={<><CreditCardOutlined /> 充值/售卡</>} size="small">
        {rechargePaymentMethods.length === 0 ? (
          <Empty description="未配置任何用于充值/售卡的支付方式。" />
        ) : (
          rechargePaymentMethods.map(pm => (
            <Row key={pm.id} gutter={16} style={{ marginBottom: 8 }} align="middle">
              <Col span={6}>
                <Text>{pm.name}</Text>
              </Col>
              <Col span={9}>
                <InputNumber
                  addonBefore="金额"
                  style={{ width: '100%' }}
                  value={rechargeData[pm.id]?.amount}
                  onChange={(value) => handleRechargeDataChange(pm.id, 'amount', value)}
                  placeholder="请输入金额"
                />
              </Col>
              <Col span={9}>
                <InputNumber
                  addonBefore="笔数"
                  style={{ width: '100%' }}
                  value={rechargeData[pm.id]?.orders}
                  onChange={(value) => handleRechargeDataChange(pm.id, 'orders', value)}
                  placeholder="请输入笔数"
                />
              </Col>
            </Row>
          ))
        )}
      </Card>
    </Spin>
  );

  if (initialLoading) {
    return <Spin tip="正在加载上报详情..." style={{ display: 'block', marginTop: 50 }} />;
  }
  
  return (
    <div className="sales-report-form-page">
      <Card
        title={isEditing ? '编辑销售上报' : '创建销售上报'}
        extra={
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
              loading={loading}
            >
              {isEditing ? '更新' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="store_id"
                label="选择门店"
                rules={[{ required: true, message: '请选择门店!' }]}
              >
                <Select 
                  placeholder="请选择门店"
                  onChange={setSelectedStoreId}
                  disabled={isEditing}
                >
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>{store.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="report_date"
                label="上报日期"
                rules={[{ required: true, message: '请选择上报日期!' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
               <Form.Item
                name="report_type"
                label="报告类型"
                rules={[{ required: true, message: '请选择报告类型!' }]}
                initialValue="daily"
              >
                <Select>
                  <Option value="shift">班次</Option>
                  <Option value="daily">日报</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row gutter={24}>
              <Col xs={24} md={16}>
                  <Tabs activeKey={activeTab} onChange={setActiveTab}>
                      <TabPane tab="销售额" key="sales">
                          {salesSection}
                      </TabPane>
                      <TabPane tab="充值/售卡" key="recharge">
                          {rechargeSection}
                      </TabPane>
                  </Tabs>
              </Col>

              <Col xs={24} md={8}>
                   <Card title="数据总览" bordered={false}>
                      <Statistic title="销售总额" value={totalSalesAmount} precision={2} prefix="¥" />
                      <Statistic title="销售总笔数" value={totalSalesOrders} />
                      <Divider />
                      <Statistic title="充值/售卡总额" value={totalRechargeAmount} precision={2} prefix="¥" />
                      <Statistic title="充值/售卡总笔数" value={totalRechargeOrders} />
                   </Card>
              </Col>
          </Row>
          
          <Divider />

          <Form.Item name="notes" label="备注">
            <TextArea rows={4} placeholder="请输入备注信息" />
          </Form.Item>

        </Form>
      </Card>
    </div>
  );
};

export default SalesReportForm;
