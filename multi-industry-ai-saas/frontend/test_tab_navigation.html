<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全渠道运营插件 Tab 导航测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #1890ff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .tabs {
            display: flex;
            background: #fafafa;
            border-bottom: 1px solid #d9d9d9;
            overflow-x: auto;
        }
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border: none;
            background: none;
            white-space: nowrap;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
        }
        .tab:hover {
            background: #e6f7ff;
        }
        .tab.active {
            background: white;
            border-bottom-color: #1890ff;
            color: #1890ff;
        }
        .content {
            padding: 24px;
            min-height: 400px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
        }
        .test-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 全渠道运营助手 - Tab 导航测试</h1>
            <p>测试插件各个功能模块的tab切换是否正常工作</p>
        </div>
        
        <div class="test-info">
            <strong>测试说明：</strong>
            <ul>
                <li>点击下方各个tab，检查是否能正确切换到对应的功能模块</li>
                <li>检查URL是否正确更新为对应的路径</li>
                <li>每个tab应该显示不同的内容，而不是都显示"运营总览"</li>
                <li>刷新页面后应该保持在当前选中的tab</li>
            </ul>
        </div>

        <div class="tabs">
            <button class="tab active" data-tab="dashboard">
                📊 运营总览
            </button>
            <button class="tab" data-tab="channels">
                🏪 渠道管理
            </button>
            <button class="tab" data-tab="products">
                📦 商品管理
            </button>
            <button class="tab" data-tab="pricing">
                💰 定价策略
            </button>
            <button class="tab" data-tab="competitors">
                👁️ 竞品监控
            </button>
            <button class="tab" data-tab="automation">
                ⚡ 自动化规则
            </button>
            <button class="tab" data-tab="analytics">
                📈 数据分析
            </button>
            <button class="tab" data-tab="ai-agent">
                🤖 AI智能体
            </button>
            <button class="tab" data-tab="ai-image">
                🎨 AI图片生成
            </button>
        </div>

        <div class="content">
            <div id="status" class="status">
                当前选中: <span id="current-tab">运营总览</span> | 
                URL路径: <span id="current-url">/project/test-project/plugins/multi-channel-operations/dashboard</span>
            </div>

            <div class="tab-content active" data-content="dashboard">
                <h2>📊 运营总览</h2>
                <p>这里是运营总览页面，显示整体运营数据和关键指标。</p>
                <ul>
                    <li>总销售额统计</li>
                    <li>各渠道表现对比</li>
                    <li>热销商品排行</li>
                    <li>运营趋势分析</li>
                </ul>
            </div>

            <div class="tab-content" data-content="channels">
                <h2>🏪 渠道管理</h2>
                <p>这里是渠道管理页面，管理各个销售渠道的配置和状态。</p>
                <ul>
                    <li>美团外卖配置</li>
                    <li>抖音小店设置</li>
                    <li>饿了么接入</li>
                    <li>京东到家配置</li>
                </ul>
            </div>

            <div class="tab-content" data-content="products">
                <h2>📦 商品管理</h2>
                <p>这里是商品管理页面，管理商品信息和多渠道同步。</p>
                <ul>
                    <li>商品信息维护</li>
                    <li>多渠道商品映射</li>
                    <li>库存同步管理</li>
                    <li>商品上下架控制</li>
                </ul>
            </div>

            <div class="tab-content" data-content="pricing">
                <h2>💰 定价策略</h2>
                <p>这里是定价策略页面，制定和执行智能定价策略。</p>
                <ul>
                    <li>动态定价规则</li>
                    <li>竞品价格监控</li>
                    <li>促销活动定价</li>
                    <li>利润率优化</li>
                </ul>
            </div>

            <div class="tab-content" data-content="competitors">
                <h2>👁️ 竞品监控</h2>
                <p>这里是竞品监控页面，实时监控竞争对手的动态。</p>
                <ul>
                    <li>竞品价格追踪</li>
                    <li>促销活动监控</li>
                    <li>新品上架提醒</li>
                    <li>市场趋势分析</li>
                </ul>
            </div>

            <div class="tab-content" data-content="automation">
                <h2>⚡ 自动化规则</h2>
                <p>这里是自动化规则页面，设置各种自动化运营规则。</p>
                <ul>
                    <li>价格自动调整</li>
                    <li>库存自动补货</li>
                    <li>促销自动执行</li>
                    <li>异常自动处理</li>
                </ul>
            </div>

            <div class="tab-content" data-content="analytics">
                <h2>📈 数据分析</h2>
                <p>这里是数据分析页面，深度分析运营数据和趋势。</p>
                <ul>
                    <li>销售数据分析</li>
                    <li>渠道效果对比</li>
                    <li>用户行为分析</li>
                    <li>ROI效果评估</li>
                </ul>
            </div>

            <div class="tab-content" data-content="ai-agent">
                <h2>🤖 AI智能体</h2>
                <p>这里是AI智能体页面，与AI助手对话并执行智能任务。</p>
                <ul>
                    <li>智能对话助手</li>
                    <li>自动化任务执行</li>
                    <li>数据智能分析</li>
                    <li>运营建议生成</li>
                </ul>
            </div>

            <div class="tab-content" data-content="ai-image">
                <h2>🎨 AI图片生成</h2>
                <p>这里是AI图片生成页面，为商品生成专业的营销图片。</p>
                <ul>
                    <li>商品主图生成</li>
                    <li>营销海报制作</li>
                    <li>多风格图片输出</li>
                    <li>批量图片处理</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Tab切换功能
        const tabs = document.querySelectorAll('.tab');
        const contents = document.querySelectorAll('.tab-content');
        const currentTabSpan = document.getElementById('current-tab');
        const currentUrlSpan = document.getElementById('current-url');
        const statusDiv = document.getElementById('status');

        // Tab名称映射
        const tabNames = {
            'dashboard': '运营总览',
            'channels': '渠道管理', 
            'products': '商品管理',
            'pricing': '定价策略',
            'competitors': '竞品监控',
            'automation': '自动化规则',
            'analytics': '数据分析',
            'ai-agent': 'AI智能体',
            'ai-image': 'AI图片生成'
        };

        function switchTab(tabKey) {
            // 移除所有active类
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.classList.remove('active'));

            // 添加active类到当前tab
            const currentTab = document.querySelector(`[data-tab="${tabKey}"]`);
            const currentContent = document.querySelector(`[data-content="${tabKey}"]`);
            
            if (currentTab && currentContent) {
                currentTab.classList.add('active');
                currentContent.classList.add('active');
                
                // 更新状态显示
                currentTabSpan.textContent = tabNames[tabKey] || tabKey;
                currentUrlSpan.textContent = `/project/test-project/plugins/multi-channel-operations/${tabKey}`;
                
                // 更新状态样式
                statusDiv.className = 'status';
                
                // 模拟URL更新
                if (history.pushState) {
                    history.pushState(null, '', `#${tabKey}`);
                }
                
                console.log(`✅ 成功切换到: ${tabNames[tabKey]} (${tabKey})`);
            } else {
                statusDiv.className = 'status error';
                console.error(`❌ 找不到tab: ${tabKey}`);
            }
        }

        // 绑定点击事件
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabKey = tab.getAttribute('data-tab');
                switchTab(tabKey);
            });
        });

        // 页面加载时检查URL hash
        window.addEventListener('load', () => {
            const hash = window.location.hash.substring(1);
            if (hash && tabNames[hash]) {
                switchTab(hash);
            }
        });

        // 监听浏览器前进后退
        window.addEventListener('popstate', () => {
            const hash = window.location.hash.substring(1);
            if (hash && tabNames[hash]) {
                switchTab(hash);
            } else {
                switchTab('dashboard');
            }
        });

        console.log('🚀 Tab导航测试页面已加载');
        console.log('📝 请点击各个tab测试切换功能');
    </script>
</body>
</html>
