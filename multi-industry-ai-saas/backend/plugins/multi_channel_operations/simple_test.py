#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手插件简化测试
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_plugin_structure():
    """测试插件文件结构"""
    logger.info("🔍 测试插件文件结构...")
    
    plugin_dir = Path(__file__).parent
    required_files = [
        "__init__.py",
        "plugin.py", 
        "db_init.py",
        "api/__init__.py",
        "api/dashboard.py",
        "api/products.py",
        "api/channels.py",
        "api/ai_agent.py",
        "api/ai_image_generator.py",
        "api/category_mapping.py",
        "api/platform_activities.py",
        "services/competitor_scraper.py",
        "models/enhanced_category.py"
    ]
    
    results = []
    for file_path in required_files:
        full_path = plugin_dir / file_path
        exists = full_path.exists()
        status = "✅" if exists else "❌"
        results.append({"file": file_path, "exists": exists, "status": status})
        logger.info(f"{status} {file_path}")
    
    return results

def test_api_imports():
    """测试API模块导入"""
    logger.info("📦 测试API模块导入...")
    
    api_modules = [
        "dashboard_router",
        "channels_router", 
        "products_router",
        "pricing_router",
        "competitors_router",
        "automation_router",
        "analytics_router",
        "settings_router",
        "ai_agent_router",
        "ai_image_generator_router",
        "category_mapping_router",
        "platform_activities_router"
    ]
    
    results = []
    
    try:
        # 添加插件目录到Python路径
        plugin_dir = Path(__file__).parent
        sys.path.insert(0, str(plugin_dir))

        # 检查API文件是否存在
        api_dir = plugin_dir / "api"

        for module_name in api_modules:
            try:
                # 检查对应的API文件是否存在
                api_file = api_dir / f"{module_name.replace('_router', '')}.py"
                exists = api_file.exists()
                status = "✅" if exists else "❌"
                results.append({"module": module_name, "exists": exists, "status": status})
                logger.info(f"{status} {module_name} - {api_file.name}")
            except Exception as e:
                results.append({"module": module_name, "exists": False, "status": "❌", "error": str(e)})
                logger.error(f"❌ {module_name} - {e}")

    except Exception as e:
        logger.error(f"检查API模块失败: {e}")
        results.append({"module": "api", "exists": False, "status": "❌", "error": str(e)})
    
    return results

def test_plugin_info():
    """测试插件信息"""
    logger.info("ℹ️ 测试插件信息...")
    
    try:
        # 导入插件信息
        plugin_dir = Path(__file__).parent
        sys.path.insert(0, str(plugin_dir))
        
        from __init__ import plugin_info
        
        required_fields = [
            "name", "code", "description", "version", 
            "author", "category", "features"
        ]
        
        results = []
        for field in required_fields:
            exists = field in plugin_info
            value = plugin_info.get(field, "未设置")
            status = "✅" if exists else "❌"
            results.append({"field": field, "exists": exists, "value": value, "status": status})
            logger.info(f"{status} {field}: {value}")
        
        return results
        
    except Exception as e:
        logger.error(f"获取插件信息失败: {e}")
        return [{"field": "plugin_info", "exists": False, "status": "❌", "error": str(e)}]

def test_database_schema():
    """测试数据库模式"""
    logger.info("🗄️ 测试数据库模式...")
    
    try:
        plugin_dir = Path(__file__).parent
        sys.path.insert(0, str(plugin_dir))
        
        from models.enhanced_category import (
            EnhancedCategory,
            PlatformCategoryMapping,
            PlatformCategory,
            ProductCategoryAttribute,
            CategoryMappingLog
        )
        
        models = [
            ("EnhancedCategory", EnhancedCategory),
            ("PlatformCategoryMapping", PlatformCategoryMapping),
            ("PlatformCategory", PlatformCategory),
            ("ProductCategoryAttribute", ProductCategoryAttribute),
            ("CategoryMappingLog", CategoryMappingLog)
        ]
        
        results = []
        for name, model_class in models:
            try:
                # 检查模型是否有必要的属性
                has_tablename = hasattr(model_class, '__tablename__')
                has_id = hasattr(model_class, 'id')
                status = "✅" if has_tablename and has_id else "❌"
                
                results.append({
                    "model": name,
                    "tablename": getattr(model_class, '__tablename__', None),
                    "has_id": has_id,
                    "status": status
                })
                logger.info(f"{status} {name} - {getattr(model_class, '__tablename__', 'No table')}")
                
            except Exception as e:
                results.append({"model": name, "status": "❌", "error": str(e)})
                logger.error(f"❌ {name} - {e}")
        
        return results
        
    except Exception as e:
        logger.error(f"测试数据库模式失败: {e}")
        return [{"model": "database_schema", "status": "❌", "error": str(e)}]

def test_services():
    """测试服务模块"""
    logger.info("⚙️ 测试服务模块...")
    
    try:
        plugin_dir = Path(__file__).parent
        sys.path.insert(0, str(plugin_dir))
        
        from services.competitor_scraper import (
            CompetitorScraperService,
            MeituanScraper,
            ElemeScraper,
            DouyinScraper
        )
        
        services = [
            ("CompetitorScraperService", CompetitorScraperService),
            ("MeituanScraper", MeituanScraper),
            ("ElemeScraper", ElemeScraper),
            ("DouyinScraper", DouyinScraper)
        ]
        
        results = []
        for name, service_class in services:
            try:
                # 检查服务类是否可以实例化
                instance = service_class()
                status = "✅"
                results.append({"service": name, "status": status})
                logger.info(f"{status} {name}")
                
            except Exception as e:
                results.append({"service": name, "status": "❌", "error": str(e)})
                logger.error(f"❌ {name} - {e}")
        
        return results
        
    except Exception as e:
        logger.error(f"测试服务模块失败: {e}")
        return [{"service": "services", "status": "❌", "error": str(e)}]

def generate_report(structure_results, import_results, info_results, db_results, service_results):
    """生成测试报告"""
    logger.info("\n" + "="*80)
    logger.info("📊 全渠道运营助手插件测试报告")
    logger.info("="*80)
    
    # 文件结构测试
    logger.info("\n📁 文件结构测试:")
    passed_structure = sum(1 for r in structure_results if r["exists"])
    logger.info(f"通过: {passed_structure}/{len(structure_results)}")
    
    # API导入测试
    logger.info("\n📦 API导入测试:")
    passed_import = sum(1 for r in import_results if r["exists"])
    logger.info(f"通过: {passed_import}/{len(import_results)}")
    
    # 插件信息测试
    logger.info("\nℹ️ 插件信息测试:")
    passed_info = sum(1 for r in info_results if r["exists"])
    logger.info(f"通过: {passed_info}/{len(info_results)}")
    
    # 数据库模式测试
    logger.info("\n🗄️ 数据库模式测试:")
    passed_db = sum(1 for r in db_results if r.get("has_id", False))
    logger.info(f"通过: {passed_db}/{len(db_results)}")
    
    # 服务模块测试
    logger.info("\n⚙️ 服务模块测试:")
    passed_service = sum(1 for r in service_results if r["status"] == "✅")
    logger.info(f"通过: {passed_service}/{len(service_results)}")
    
    # 总结
    total_tests = len(structure_results) + len(import_results) + len(info_results) + len(db_results) + len(service_results)
    total_passed = passed_structure + passed_import + passed_info + passed_db + passed_service
    success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    
    logger.info(f"\n📈 测试总结:")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过数: {total_passed}")
    logger.info(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        logger.info("🎉 插件基础结构测试通过！")
    elif success_rate >= 60:
        logger.info("⚠️ 插件基本可用，但需要优化")
    else:
        logger.info("❌ 插件存在严重问题，需要修复")
    
    logger.info("\n💡 建议:")
    logger.info("1. 确保所有必要文件存在")
    logger.info("2. 修复导入错误")
    logger.info("3. 完善插件信息")
    logger.info("4. 验证数据库模型")
    logger.info("5. 测试服务功能")
    
    logger.info("="*80)

def main():
    """主函数"""
    logger.info("🚀 开始全渠道运营助手插件基础测试")
    
    # 运行各项测试
    structure_results = test_plugin_structure()
    import_results = test_api_imports()
    info_results = test_plugin_info()
    db_results = test_database_schema()
    service_results = test_services()
    
    # 生成报告
    generate_report(structure_results, import_results, info_results, db_results, service_results)

if __name__ == "__main__":
    main()
