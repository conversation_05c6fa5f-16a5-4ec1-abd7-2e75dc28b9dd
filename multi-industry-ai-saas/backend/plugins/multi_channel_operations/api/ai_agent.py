#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - AI智能体API
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel, Field

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..models.operation_log import AIAgentTask
from ..services.ai_agent import AIAgentService
from ..services.ai_integration_service import MultiChannelAIIntegrationService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["全渠道运营-AI智能体"])

# Pydantic模型
class ChatRequest(BaseModel):
    message: str = Field(..., description="用户消息")
    conversation_id: Optional[str] = Field(None, description="对话ID")

class ChatResponse(BaseModel):
    success: bool
    task_id: str
    response: str
    actions: List[str]
    data: Dict[str, Any]

class TaskResponse(BaseModel):
    id: str
    task_type: str
    task_name: str
    task_description: str
    user_input: str
    conversation_id: Optional[str]
    ai_response: Optional[str]
    ai_actions: List[str]
    status: str
    progress: int
    result_data: Dict[str, Any]
    user_feedback: Optional[str]
    feedback_note: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]
    created_at: str
    updated_at: str

# 初始化服务
ai_agent_service = AIAgentService()


@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai_agent(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """与AI智能体对话 - 使用集成的AI助理系统"""
    try:
        # 使用新的AI集成服务
        result = await MultiChannelAIIntegrationService.chat_with_assistant(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            message=request.message,
            conversation_id=request.conversation_id,
            context={
                "source": "multi_channel_operations_plugin",
                "user_role": "operator"
            }
        )

        # 转换响应格式以保持兼容性
        return ChatResponse(
            success=True,
            task_id=result.get("thread_id", ""),
            response=result.get("content", ""),
            actions=result.get("tool_calls", []),
            data={
                "assistant_id": result.get("assistant_id"),
                "message_id": result.get("message_id"),
                "usage": result.get("usage", {})
            }
        )

    except Exception as e:
        logger.error(f"AI智能体对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI智能体对话失败: {str(e)}")


@router.get("/tasks", response_model=List[TaskResponse])
async def get_ai_tasks(
    task_type: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI任务列表"""
    try:
        # 构建查询条件
        conditions = [AIAgentTask.project_id == project.id]

        if task_type:
            conditions.append(AIAgentTask.task_type == task_type)
        if status:
            conditions.append(AIAgentTask.status == status)

        # 执行查询
        stmt = select(AIAgentTask).where(
            and_(*conditions)
        ).offset(skip).limit(limit).order_by(AIAgentTask.created_at.desc())

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        # 转换为响应格式
        task_list = [
            TaskResponse(
                id=str(task.id),
                task_type=task.task_type,
                task_name=task.task_name,
                task_description=task.task_description or "",
                user_input=task.user_input,
                conversation_id=task.conversation_id,
                ai_response=task.ai_response,
                ai_actions=task.ai_actions or [],
                status=task.status,
                progress=task.progress,
                result_data=task.result_data or {},
                user_feedback=task.user_feedback,
                feedback_note=task.feedback_note,
                started_at=task.started_at.isoformat() if task.started_at else None,
                completed_at=task.completed_at.isoformat() if task.completed_at else None,
                created_at=task.created_at.isoformat(),
                updated_at=task.updated_at.isoformat()
            )
            for task in tasks
        ]

        return {
            "success": True,
            "data": task_list
        }

    except Exception as e:
        logger.error(f"获取AI任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI任务列表失败: {str(e)}")


@router.get("/tasks/{task_id}")
async def get_ai_task_detail(
    task_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI任务详情"""
    try:
        stmt = select(AIAgentTask).where(
            and_(
                AIAgentTask.id == task_id,
                AIAgentTask.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="AI任务不存在")

        return {
            "id": str(task.id),
            "task_type": task.task_type,
            "task_name": task.task_name,
            "task_description": task.task_description,
            "user_input": task.user_input,
            "conversation_id": task.conversation_id,
            "ai_model": task.ai_model,
            "ai_response": task.ai_response,
            "ai_actions": task.ai_actions or [],
            "status": task.status,
            "progress": task.progress,
            "result_data": task.result_data or {},
            "affected_items": task.affected_items or [],
            "user_feedback": task.user_feedback,
            "feedback_note": task.feedback_note,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI任务详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI任务详情失败: {str(e)}")


@router.post("/tasks/{task_id}/feedback")
async def submit_task_feedback(
    task_id: str,
    feedback: str = Body(..., description="用户反馈"),
    feedback_note: Optional[str] = Body(None, description="反馈备注"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """提交任务反馈"""
    try:
        stmt = select(AIAgentTask).where(
            and_(
                AIAgentTask.id == task_id,
                AIAgentTask.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="AI任务不存在")

        # 更新反馈
        task.user_feedback = feedback
        task.feedback_note = feedback_note

        await db.commit()

        return {
            "success": True,
            "message": "反馈提交成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交任务反馈失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务反馈失败: {str(e)}")


@router.get("/capabilities")
async def get_ai_capabilities(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI智能体能力列表 - 使用集成的AI助理系统"""
    try:
        # 使用新的AI集成服务获取能力信息
        capabilities = await MultiChannelAIIntegrationService.get_assistant_capabilities(
            db=db,
            project_id=project.id
        )

        return {
            "success": True,
            "data": capabilities
        }

    except Exception as e:
        logger.error(f"获取AI能力列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI能力列表失败: {str(e)}")


@router.get("/conversation-history/{conversation_id}")
async def get_conversation_history(
    conversation_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取对话历史"""
    try:
        stmt = select(AIAgentTask).where(
            and_(
                AIAgentTask.conversation_id == conversation_id,
                AIAgentTask.project_id == project.id
            )
        ).order_by(AIAgentTask.created_at.asc())

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        conversation = []
        for task in tasks:
            conversation.append({
                "role": "user",
                "content": task.user_input,
                "timestamp": task.created_at.isoformat()
            })

            if task.ai_response:
                conversation.append({
                    "role": "assistant",
                    "content": task.ai_response,
                    "timestamp": task.completed_at.isoformat() if task.completed_at else task.created_at.isoformat(),
                    "actions": task.ai_actions or [],
                    "task_id": str(task.id)
                })

        return {
            "success": True,
            "conversation_id": conversation_id,
            "messages": conversation
        }

    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话历史失败: {str(e)}")