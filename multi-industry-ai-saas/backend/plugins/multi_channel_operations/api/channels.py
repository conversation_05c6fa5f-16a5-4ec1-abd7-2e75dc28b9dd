#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 渠道管理API
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel, Field

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..models.channel_config import MultiChannelConfig
from ..services.platform_adapter import PlatformAdapterManager

logger = logging.getLogger(__name__)
router = APIRouter(tags=["全渠道运营-渠道管理"])

# Pydantic模型
class ChannelConfigRequest(BaseModel):
    channel_id: str = Field(..., description="渠道ID")
    platform_config: Dict[str, Any] = Field(default_factory=dict, description="平台配置")
    api_credentials: Dict[str, Any] = Field(..., description="API凭证")
    sync_settings: Dict[str, Any] = Field(default_factory=dict, description="同步设置")

class ChannelConfigResponse(BaseModel):
    id: str
    channel_id: str
    platform_config: Dict[str, Any]
    sync_settings: Dict[str, Any]
    is_active: bool
    created_at: str
    updated_at: str

class ConnectionTestRequest(BaseModel):
    platform_code: str = Field(..., description="平台代码")
    api_credentials: Dict[str, Any] = Field(..., description="API凭证")
    platform_config: Dict[str, Any] = Field(default_factory=dict, description="平台配置")

# 初始化服务
platform_manager = PlatformAdapterManager()


@router.get("/configs", response_model=List[ChannelConfigResponse])
async def get_channel_configs(
    channel_id: Optional[str] = Query(None, description="渠道ID过滤"),
    is_active: Optional[bool] = Query(None, description="是否启用过滤"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(50, description="限制数量"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取渠道配置列表"""
    try:
        # 构建查询条件
        conditions = [MultiChannelConfig.project_id == project.id]

        if channel_id:
            conditions.append(MultiChannelConfig.channel_id == channel_id)
        if is_active is not None:
            conditions.append(MultiChannelConfig.is_active == is_active)

        # 执行查询
        stmt = select(MultiChannelConfig).where(
            and_(*conditions)
        ).offset(skip).limit(limit).order_by(MultiChannelConfig.created_at.desc())

        result = await db.execute(stmt)
        configs = result.scalars().all()

        # 转换为响应格式（隐藏敏感信息）
        return [
            ChannelConfigResponse(
                id=str(config.id),
                channel_id=config.channel_id,
                platform_config=config.platform_config or {},
                sync_settings=config.sync_settings or {},
                is_active=config.is_active,
                created_at=config.created_at.isoformat(),
                updated_at=config.updated_at.isoformat()
            )
            for config in configs
        ]

    except Exception as e:
        logger.error(f"获取渠道配置列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取渠道配置列表失败: {str(e)}")


@router.post("/configs")
async def create_channel_config(
    request: ChannelConfigRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建渠道配置"""
    try:
        # 检查是否已存在配置
        stmt = select(MultiChannelConfig).where(
            and_(
                MultiChannelConfig.project_id == project.id,
                MultiChannelConfig.channel_id == request.channel_id
            )
        )
        result = await db.execute(stmt)
        existing_config = result.scalar_one_or_none()

        if existing_config:
            raise HTTPException(status_code=400, detail="该渠道配置已存在")

        # 创建新配置
        config = MultiChannelConfig(
            project_id=project.id,
            channel_id=request.channel_id,
            platform_config=request.platform_config,
            api_credentials=request.api_credentials,  # 实际应用中需要加密存储
            sync_settings=request.sync_settings,
            is_active=True
        )

        db.add(config)
        await db.commit()

        return {
            "success": True,
            "message": "渠道配置创建成功",
            "config_id": str(config.id)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建渠道配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建渠道配置失败: {str(e)}")


@router.put("/configs/{config_id}")
async def update_channel_config(
    config_id: str,
    request: ChannelConfigRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新渠道配置"""
    try:
        # 查找配置
        stmt = select(MultiChannelConfig).where(
            and_(
                MultiChannelConfig.id == config_id,
                MultiChannelConfig.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(status_code=404, detail="渠道配置不存在")

        # 更新配置
        config.platform_config = request.platform_config
        config.api_credentials = request.api_credentials
        config.sync_settings = request.sync_settings

        await db.commit()

        return {
            "success": True,
            "message": "渠道配置更新成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新渠道配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新渠道配置失败: {str(e)}")


@router.delete("/configs/{config_id}")
async def delete_channel_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除渠道配置"""
    try:
        # 查找配置
        stmt = select(MultiChannelConfig).where(
            and_(
                MultiChannelConfig.id == config_id,
                MultiChannelConfig.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(status_code=404, detail="渠道配置不存在")

        # 删除配置
        await db.delete(config)
        await db.commit()

        return {
            "success": True,
            "message": "渠道配置删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除渠道配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除渠道配置失败: {str(e)}")


@router.post("/test-connection")
async def test_platform_connection(
    request: ConnectionTestRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """测试平台API连接"""
    try:
        # 构建配置
        config = {
            'platform_code': request.platform_code,
            'api_credentials': request.api_credentials,
            'platform_config': request.platform_config
        }

        # 获取平台适配器
        adapter = platform_manager.get_adapter(request.platform_code, config)

        if not adapter:
            return {
                "success": False,
                "message": f"不支持的平台: {request.platform_code}"
            }

        # 测试连接
        result = await adapter.test_connection()

        return {
            "success": result.get("success", False),
            "message": result.get("message", "连接测试完成"),
            "data": result.get("shop_info", {})
        }

    except Exception as e:
        logger.error(f"测试平台连接失败: {e}")
        return {
            "success": False,
            "message": f"连接测试异常: {str(e)}"
        }


@router.get("/supported-platforms")
async def get_supported_platforms():
    """获取支持的平台列表"""
    try:
        platforms = platform_manager.get_supported_platforms()

        return {
            "success": True,
            "data": platforms
        }

    except Exception as e:
        logger.error(f"获取支持平台列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取支持平台列表失败: {str(e)}")


@router.post("/batch-test-connections")
async def batch_test_connections(
    configs: Dict[str, Dict[str, Any]] = Body(..., description="平台配置字典"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """批量测试平台连接"""
    try:
        results = await platform_manager.test_all_connections(configs)

        # 统计结果
        total_count = len(results)
        success_count = sum(1 for r in results.values() if r.get('success'))

        return {
            "success": True,
            "message": f"连接测试完成，成功{success_count}/{total_count}个平台",
            "total_count": total_count,
            "success_count": success_count,
            "results": results
        }

    except Exception as e:
        logger.error(f"批量测试连接失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量测试连接失败: {str(e)}")


@router.post("/configs/{config_id}/toggle")
async def toggle_channel_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """切换渠道配置启用状态"""
    try:
        # 查找配置
        stmt = select(MultiChannelConfig).where(
            and_(
                MultiChannelConfig.id == config_id,
                MultiChannelConfig.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(status_code=404, detail="渠道配置不存在")

        # 切换状态
        config.is_active = not config.is_active
        await db.commit()

        return {
            "success": True,
            "message": f"渠道配置已{'启用' if config.is_active else '禁用'}",
            "is_active": config.is_active
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换渠道配置状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"切换渠道配置状态失败: {str(e)}")