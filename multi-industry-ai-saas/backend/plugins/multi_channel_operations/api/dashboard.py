#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 运营总览API
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func, desc

from core.auth import get_current_user
from api.deps import get_current_project
from db.database import get_db
from models.user import User
from models.project import Project
from models.sales_management import SalesChannel
from ..models.channel_config import MultiChannelConfig
from ..models.product_mapping import MultiChannelProductMapping
from ..models.operation_log import MultiChannelOperationLog
from ..services.dashboard_service import DashboardService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["全渠道运营-总览"])

@router.get("/overview")
async def get_dashboard_overview(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取运营总览数据"""
    try:
        # 获取渠道统计
        channels_query = select(SalesChannel).where(
            and_(
                SalesChannel.project_id == current_project.id,
                SalesChannel.is_active == True
            )
        )
        channels_result = await db.execute(channels_query)
        channels = channels_result.scalars().all()
        
        # 获取已配置的渠道
        configs_query = select(MultiChannelConfig).where(
            and_(
                MultiChannelConfig.project_id == current_project.id,
                MultiChannelConfig.is_active == True
            )
        )
        configs_result = await db.execute(configs_query)
        configs = configs_result.scalars().all()
        
        # 获取商品映射统计
        mappings_query = select(func.count(MultiChannelProductMapping.id)).where(
            MultiChannelProductMapping.project_id == current_project.id
        )
        mappings_result = await db.execute(mappings_query)
        total_mappings = mappings_result.scalar() or 0
        
        # 获取今日操作统计
        today = datetime.now().date()
        today_logs_query = select(func.count(MultiChannelOperationLog.id)).where(
            and_(
                MultiChannelOperationLog.project_id == current_project.id,
                func.date(MultiChannelOperationLog.executed_at) == today
            )
        )
        today_logs_result = await db.execute(today_logs_query)
        today_operations = today_logs_result.scalar() or 0
        
        # 获取渠道健康状态
        channel_health = await DashboardService.get_channel_health_status(
            db, current_project.id
        )
        
        # 获取最近操作记录
        recent_logs_query = select(MultiChannelOperationLog).where(
            MultiChannelOperationLog.project_id == current_project.id
        ).order_by(desc(MultiChannelOperationLog.executed_at)).limit(10)
        recent_logs_result = await db.execute(recent_logs_query)
        recent_logs = recent_logs_result.scalars().all()
        
        # 构建响应数据
        overview_data = {
            "summary": {
                "total_channels": len(channels),
                "configured_channels": len(configs),
                "total_product_mappings": total_mappings,
                "today_operations": today_operations,
                "channel_health_score": channel_health.get("overall_score", 0)
            },
            "channel_status": {
                "active_channels": len([c for c in configs if c.is_active]),
                "inactive_channels": len([c for c in configs if not c.is_active]),
                "api_configured": len([c for c in configs if c.is_api_configured()]),
                "api_not_configured": len([c for c in configs if not c.is_api_configured()])
            },
            "channel_health": channel_health,
            "recent_operations": [
                {
                    "id": str(log.id),
                    "operation_type": log.operation_type,
                    "target_type": log.target_type,
                    "result_status": log.result_status,
                    "result_message": log.result_message,
                    "executed_at": log.executed_at.isoformat() if log.executed_at else None
                }
                for log in recent_logs
            ]
        }
        
        return {
            "success": True,
            "data": overview_data
        }
        
    except Exception as e:
        logger.error(f"获取运营总览失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运营总览失败: {str(e)}"
        )

@router.get("/channel-performance")
async def get_channel_performance(
    days: int = 7,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取渠道性能数据"""
    try:
        performance_data = await DashboardService.get_channel_performance(
            db, current_project.id, days
        )
        
        return {
            "success": True,
            "data": performance_data
        }
        
    except Exception as e:
        logger.error(f"获取渠道性能数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取渠道性能数据失败: {str(e)}"
        )

@router.get("/operation-trends")
async def get_operation_trends(
    days: int = 30,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取操作趋势数据"""
    try:
        # 获取指定天数内的操作统计
        start_date = datetime.now() - timedelta(days=days)
        
        trends_query = select(
            func.date(MultiChannelOperationLog.executed_at).label("date"),
            MultiChannelOperationLog.operation_type,
            func.count(MultiChannelOperationLog.id).label("count")
        ).where(
            and_(
                MultiChannelOperationLog.project_id == current_project.id,
                MultiChannelOperationLog.executed_at >= start_date
            )
        ).group_by(
            func.date(MultiChannelOperationLog.executed_at),
            MultiChannelOperationLog.operation_type
        ).order_by(func.date(MultiChannelOperationLog.executed_at))
        
        trends_result = await db.execute(trends_query)
        trends_data = trends_result.all()
        
        # 组织数据
        trends_by_date = {}
        operation_types = set()
        
        for row in trends_data:
            date_str = row.date.isoformat()
            operation_type = row.operation_type
            count = row.count
            
            if date_str not in trends_by_date:
                trends_by_date[date_str] = {}
            
            trends_by_date[date_str][operation_type] = count
            operation_types.add(operation_type)
        
        # 构建时间序列数据
        trends_series = []
        for operation_type in sorted(operation_types):
            series_data = []
            for date_str in sorted(trends_by_date.keys()):
                series_data.append({
                    "date": date_str,
                    "value": trends_by_date[date_str].get(operation_type, 0)
                })
            
            trends_series.append({
                "operation_type": operation_type,
                "data": series_data
            })
        
        return {
            "success": True,
            "data": {
                "trends": trends_series,
                "summary": {
                    "total_operations": sum(row.count for row in trends_data),
                    "operation_types": list(operation_types),
                    "date_range": {
                        "start": start_date.date().isoformat(),
                        "end": datetime.now().date().isoformat()
                    }
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取操作趋势数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取操作趋势数据失败: {str(e)}"
        )

@router.get("/ai-insights")
async def get_ai_insights(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI智能洞察"""
    try:
        insights = await DashboardService.generate_ai_insights(
            db, current_project.id, current_user.id
        )
        
        return {
            "success": True,
            "data": insights
        }
        
    except Exception as e:
        logger.error(f"获取AI洞察失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI洞察失败: {str(e)}"
        )
