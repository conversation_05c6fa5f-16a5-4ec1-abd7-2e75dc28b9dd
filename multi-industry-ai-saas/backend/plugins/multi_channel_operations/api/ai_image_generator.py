#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - AI图片生成API
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.image_generator import AIImageGeneratorService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["全渠道运营-AI图片生成"])

# Pydantic模型
class ImageGenerationRequest(BaseModel):
    product_id: str = Field(..., description="商品ID")
    prompt: str = Field(..., description="生成提示词")
    style: str = Field(default="natural", description="图片风格")
    size: str = Field(default="1024x1024", description="图片尺寸")
    model: str = Field(default="dall-e-3", description="AI模型")
    channel_id: Optional[str] = Field(None, description="目标渠道ID")

class BatchImageGenerationRequest(BaseModel):
    requests: List[ImageGenerationRequest] = Field(..., description="批量生成请求列表")

class ChannelImageRequest(BaseModel):
    product_id: str = Field(..., description="商品ID")
    channel_id: str = Field(..., description="渠道ID")
    base_prompt: str = Field(..., description="基础提示词")

class ImageSelectionRequest(BaseModel):
    generation_id: str = Field(..., description="生成记录ID")
    image_url: str = Field(..., description="选中的图片URL")

class ImageGenerationResponse(BaseModel):
    success: bool
    generation_id: Optional[str]
    images: List[str]
    error: Optional[str]

# 初始化服务
image_generator = AIImageGeneratorService()


@router.post("/generate", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """生成AI图片"""
    try:
        result = await image_generator.generate_product_image(
            db=db,
            project_id=str(project.id),
            product_id=request.product_id,
            prompt=request.prompt,
            style=request.style,
            size=request.size,
            model=request.model,
            channel_id=request.channel_id,
            user_id=str(current_user.id)
        )

        return ImageGenerationResponse(
            success=result["success"],
            generation_id=result.get("generation_id"),
            images=result.get("images", []),
            error=result.get("error")
        )

    except Exception as e:
        logger.error(f"AI图片生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI图片生成失败: {str(e)}")


@router.post("/generate-batch")
async def batch_generate_images(
    request: BatchImageGenerationRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """批量生成AI图片"""
    try:
        # 转换请求格式
        batch_requests = []
        for req in request.requests:
            batch_requests.append({
                'product_id': req.product_id,
                'prompt': req.prompt,
                'style': req.style,
                'size': req.size,
                'model': req.model,
                'channel_id': req.channel_id
            })

        results = await image_generator.batch_generate_images(
            db=db,
            project_id=str(project.id),
            requests=batch_requests,
            user_id=str(current_user.id)
        )

        return {
            "success": True,
            "message": f"批量生成完成，共{len(results)}个任务",
            "results": results
        }

    except Exception as e:
        logger.error(f"批量图片生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量图片生成失败: {str(e)}")


@router.post("/generate-channel-specific")
async def generate_channel_specific_image(
    request: ChannelImageRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """为特定渠道生成定制化图片"""
    try:
        result = await image_generator.generate_channel_specific_image(
            db=db,
            project_id=str(project.id),
            product_id=request.product_id,
            channel_id=request.channel_id,
            base_prompt=request.base_prompt,
            user_id=str(current_user.id)
        )

        return {
            "success": result["success"],
            "message": "渠道定制图片生成成功" if result["success"] else f"生成失败: {result.get('error')}",
            "generation_id": result.get("generation_id"),
            "images": result.get("images", []),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"渠道定制图片生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"渠道定制图片生成失败: {str(e)}")


@router.post("/select-image")
async def select_image(
    request: ImageSelectionRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """选择生成的图片作为商品主图"""
    try:
        success = await image_generator.select_image_for_product(
            db=db,
            generation_id=request.generation_id,
            image_url=request.image_url,
            user_id=str(current_user.id)
        )

        return {
            "success": success,
            "message": "图片选择成功" if success else "图片选择失败"
        }

    except Exception as e:
        logger.error(f"选择图片失败: {e}")
        raise HTTPException(status_code=500, detail=f"选择图片失败: {str(e)}")


@router.get("/history")
async def get_generation_history(
    product_id: Optional[str] = Query(None, description="商品ID过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(20, description="限制数量"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取图片生成历史"""
    try:
        result = await image_generator.get_generation_history(
            db=db,
            project_id=str(project.id),
            product_id=product_id,
            limit=limit
        )

        # 如果有状态过滤，进行过滤
        if status:
            result = [item for item in result if item.get('status') == status]

        # 应用分页
        total = len(result)
        result = result[skip:skip + limit]

        return {
            "success": True,
            "data": result,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        logger.error(f"获取图片生成历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片生成历史失败: {str(e)}")


@router.get("/models")
async def get_supported_models():
    """获取支持的AI模型列表"""
    try:
        models = [
            {
                "id": "dall-e-3",
                "name": "DALL-E 3",
                "provider": "OpenAI",
                "description": "OpenAI最新的图像生成模型，质量极高",
                "max_size": "1024x1024",
                "styles": ["vivid", "natural"],
                "pricing": "高"
            },
            {
                "id": "midjourney",
                "name": "Midjourney",
                "provider": "Midjourney",
                "description": "专业的艺术图像生成模型",
                "max_size": "1024x1024",
                "styles": ["realistic", "artistic", "cartoon"],
                "pricing": "中"
            },
            {
                "id": "stable-diffusion",
                "name": "Stable Diffusion",
                "provider": "Stability AI",
                "description": "开源的高质量图像生成模型",
                "max_size": "1024x1024",
                "styles": ["photorealistic", "artistic", "anime"],
                "pricing": "低"
            }
        ]

        return {
            "success": True,
            "data": models
        }

    except Exception as e:
        logger.error(f"获取AI模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI模型列表失败: {str(e)}")


@router.get("/styles")
async def get_image_styles():
    """获取图片风格列表"""
    try:
        styles = [
            {
                "id": "natural",
                "name": "自然风格",
                "description": "自然真实的商品展示",
                "suitable_for": ["食品", "日用品", "服装"]
            },
            {
                "id": "vivid",
                "name": "鲜艳风格",
                "description": "色彩鲜艳，适合社交媒体",
                "suitable_for": ["时尚", "美妆", "电子产品"]
            },
            {
                "id": "photorealistic",
                "name": "写实风格",
                "description": "高度写实的专业摄影效果",
                "suitable_for": ["奢侈品", "汽车", "房地产"]
            },
            {
                "id": "artistic",
                "name": "艺术风格",
                "description": "艺术化的创意表现",
                "suitable_for": ["艺术品", "手工制品", "创意产品"]
            },
            {
                "id": "minimalist",
                "name": "简约风格",
                "description": "简洁清爽的设计风格",
                "suitable_for": ["科技产品", "家居用品", "办公用品"]
            }
        ]

        return {
            "success": True,
            "data": styles
        }

    except Exception as e:
        logger.error(f"获取图片风格列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片风格列表失败: {str(e)}")


@router.get("/templates")
async def get_prompt_templates():
    """获取提示词模板"""
    try:
        templates = [
            {
                "category": "食品",
                "templates": [
                    {
                        "name": "美食摄影",
                        "prompt": "高质量美食摄影，{product_name}，专业灯光，白色背景，诱人的食物展示",
                        "description": "适合餐饮类商品"
                    },
                    {
                        "name": "外卖风格",
                        "prompt": "外卖平台风格，{product_name}，暖色调，食物特写，干净整洁",
                        "description": "适合外卖平台"
                    }
                ]
            },
            {
                "category": "服装",
                "templates": [
                    {
                        "name": "时尚摄影",
                        "prompt": "时尚服装摄影，{product_name}，专业模特，工作室灯光，时尚背景",
                        "description": "适合服装类商品"
                    },
                    {
                        "name": "平铺展示",
                        "prompt": "服装平铺摄影，{product_name}，白色背景，细节清晰，专业展示",
                        "description": "适合电商平台"
                    }
                ]
            },
            {
                "category": "电子产品",
                "templates": [
                    {
                        "name": "科技风格",
                        "prompt": "科技产品摄影，{product_name}，现代简约，金属质感，专业灯光",
                        "description": "适合电子产品"
                    },
                    {
                        "name": "使用场景",
                        "prompt": "产品使用场景，{product_name}，生活化场景，自然光线，真实环境",
                        "description": "展示产品使用场景"
                    }
                ]
            }
        ]

        return {
            "success": True,
            "data": templates
        }

    except Exception as e:
        logger.error(f"获取提示词模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取提示词模板失败: {str(e)}")