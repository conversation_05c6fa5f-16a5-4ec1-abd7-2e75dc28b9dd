#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 操作日志模型
"""

import uuid
from sqlalchemy import Column, String, Boolean, ForeignKey, Text, DateTime, Integer, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

from db.database import Base


class MultiChannelOperationLog(Base):
    """多渠道操作日志模型"""
    __tablename__ = "multi_channel_operation_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)

    # 操作信息
    operation_type = Column(String(50), nullable=False, comment="操作类型")

    # 操作对象
    target_type = Column(String(50), nullable=False, comment="目标类型: product, channel, rule, batch")
    target_id = Column(UUID(as_uuid=True), nullable=True, comment="目标ID")

    # 操作数据和结果
    operation_data = Column(JSONB, default=dict, comment="操作数据")
    result_status = Column(String(50), nullable=False, comment="操作状态: success, failed, partial")
    result_message = Column(Text, nullable=True, comment="结果信息")

    # 操作者信息
    executed_by = Column(UUID(as_uuid=True), nullable=True, comment="操作用户ID")

    # 时间戳
    executed_at = Column(DateTime, default=datetime.utcnow)

    # 索引
    __table_args__ = (
        Index('idx_operation_project', 'project_id', 'executed_at'),
        Index('idx_operation_type', 'operation_type'),
        Index('idx_operation_target', 'target_type', 'target_id'),
        Index('idx_operation_user', 'executed_by'),
    )


class AIAgentTask(Base):
    """AI智能体任务记录"""
    __tablename__ = "multi_channel_ai_agent_tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 任务信息
    task_type = Column(String(50), nullable=False, comment="任务类型: analysis, optimization, automation, monitoring")
    task_name = Column(String(200), nullable=False, comment="任务名称")
    task_description = Column(Text, nullable=True, comment="任务描述")
    
    # 用户输入
    user_input = Column(Text, nullable=False, comment="用户输入")
    conversation_id = Column(String(100), nullable=True, comment="对话ID")
    
    # AI处理
    ai_model = Column(String(100), nullable=True, comment="使用的AI模型")
    ai_response = Column(Text, nullable=True, comment="AI响应")
    ai_actions = Column(JSONB, default=list, comment="AI执行的动作列表")
    
    # 任务状态
    status = Column(String(50), default="pending", comment="任务状态: pending, processing, completed, failed")
    progress = Column(Integer, default=0, comment="任务进度百分比")
    
    # 执行结果
    result_data = Column(JSONB, default=dict, comment="执行结果数据")
    affected_items = Column(Integer, default=0, comment="影响的项目数量")
    
    # 用户反馈
    user_feedback = Column(String(50), nullable=True, comment="用户反馈: satisfied, unsatisfied, neutral")
    feedback_note = Column(Text, nullable=True, comment="反馈备注")
    
    # 时间信息
    started_at = Column(DateTime, nullable=True, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class NotificationLog(Base):
    """通知日志记录"""
    __tablename__ = "multi_channel_notification_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 通知信息
    notification_type = Column(String(50), nullable=False, comment="通知类型: alert, info, warning, error")
    title = Column(String(200), nullable=False, comment="通知标题")
    message = Column(Text, nullable=False, comment="通知消息")
    
    # 通知来源
    source_type = Column(String(50), nullable=False, comment="来源类型: automation, ai_agent, competitor, system")
    source_id = Column(UUID(as_uuid=True), nullable=True, comment="来源ID")
    
    # 通知渠道
    notification_channels = Column(JSONB, default=list, comment="通知渠道列表")
    
    # 发送状态
    send_status = Column(String(50), default="pending", comment="发送状态: pending, sent, failed")
    send_details = Column(JSONB, default=dict, comment="发送详情")
    
    # 接收者信息
    recipient_users = Column(JSONB, default=list, comment="接收用户ID列表")
    
    # 处理状态
    is_read = Column(Boolean, default=False, comment="是否已读")
    read_at = Column(DateTime, nullable=True, comment="阅读时间")
    is_handled = Column(Boolean, default=False, comment="是否已处理")
    handled_at = Column(DateTime, nullable=True, comment="处理时间")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SystemHealthCheck(Base):
    """系统健康检查记录"""
    __tablename__ = "multi_channel_system_health_checks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 检查信息
    check_type = Column(String(50), nullable=False, comment="检查类型: api_connection, data_sync, automation, performance")
    check_name = Column(String(200), nullable=False, comment="检查名称")
    
    # 检查结果
    status = Column(String(50), nullable=False, comment="检查状态: healthy, warning, error")
    score = Column(Integer, nullable=True, comment="健康评分 0-100")
    
    # 详细结果
    check_details = Column(JSONB, default=dict, comment="检查详情")
    issues_found = Column(JSONB, default=list, comment="发现的问题列表")
    recommendations = Column(JSONB, default=list, comment="改进建议")
    
    # 性能指标
    response_time = Column(Integer, nullable=True, comment="响应时间（毫秒）")
    success_rate = Column(Integer, nullable=True, comment="成功率百分比")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
