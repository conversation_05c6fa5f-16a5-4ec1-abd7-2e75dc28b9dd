#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手插件 - Docker环境完整测试
"""

import pytest
import asyncio
import json
import uuid
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
from httpx import AsyncClient
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 避免导入冲突，直接测试HTTP接口
os.environ["TESTING"] = "1"

class MultiChannelPluginTester:
    """全渠道运营插件测试器"""

    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.project_id = str(uuid.uuid4())
        self.user_id = str(uuid.uuid4())
        self.headers = {
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json",
            "X-User-ID": self.user_id,
            "X-Project-ID": self.project_id
        }
    
    async def test_dashboard_endpoints(self, client: AsyncClient):
        """测试仪表板接口"""
        logger.info("🏠 测试仪表板接口...")
        
        endpoints = [
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/dashboard",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/dashboard/stats",
                "expected_fields": ["success", "data", "stats"]
            }
        ]
        
        results = []
        for endpoint in endpoints:
            try:
                if endpoint["method"] == "GET":
                    response = await client.get(endpoint["url"], headers=self.headers)
                
                result = {
                    "endpoint": endpoint["url"],
                    "method": endpoint["method"],
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 201],
                    "response_data": None,
                    "validation": {"passed": False, "errors": []}
                }
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        result["response_data"] = data
                        
                        # 验证响应字段
                        validation_errors = []
                        for field in endpoint["expected_fields"]:
                            if field not in data:
                                validation_errors.append(f"缺少字段: {field}")
                        
                        result["validation"]["passed"] = len(validation_errors) == 0
                        result["validation"]["errors"] = validation_errors
                        
                    except json.JSONDecodeError:
                        result["validation"]["errors"].append("响应不是有效的JSON")
                
                results.append(result)
                status = "✅" if result["success"] and result["validation"]["passed"] else "❌"
                logger.info(f"{status} {endpoint['method']} {endpoint['url']} - {response.status_code}")
                
            except Exception as e:
                results.append({
                    "endpoint": endpoint["url"],
                    "method": endpoint["method"],
                    "error": str(e),
                    "success": False
                })
                logger.error(f"❌ {endpoint['method']} {endpoint['url']} - {e}")
        
        return results
    
    async def test_product_endpoints(self, client: AsyncClient):
        """测试商品管理接口"""
        logger.info("🛍️ 测试商品管理接口...")
        
        endpoints = [
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/products/list",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/products/mappings",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/products/import",
                "data": {
                    "import_source": "project_products",
                    "import_options": ["basic_info", "images"],
                    "auto_create_mapping": True
                },
                "expected_fields": ["success", "message"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/products/multi-channel-sync",
                "data": {
                    "product_id": "test-product-id",
                    "channel_ids": ["channel-1", "channel-2"],
                    "sync_options": ["price", "stock"]
                },
                "expected_fields": ["success", "message"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/products/generate-image",
                "data": {
                    "product_id": "test-product-id",
                    "prompt": "高质量商品图片，白色背景",
                    "style": "natural"
                },
                "expected_fields": ["success", "message"]
            }
        ]
        
        return await self._test_endpoints(client, endpoints)
    
    async def test_ai_agent_endpoints(self, client: AsyncClient):
        """测试AI智能体接口"""
        logger.info("🤖 测试AI智能体接口...")
        
        endpoints = [
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/ai-agent/capabilities",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/ai-agent/tasks",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/ai-agent/chat",
                "data": {
                    "message": "帮我分析一下商品销售情况",
                    "conversation_id": "test-conversation"
                },
                "expected_fields": ["success", "response"]
            }
        ]
        
        return await self._test_endpoints(client, endpoints)
    
    async def test_category_mapping_endpoints(self, client: AsyncClient):
        """测试类目映射接口"""
        logger.info("🔗 测试类目映射接口...")
        
        endpoints = [
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/category-mappings",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/categories/local",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/category-mappings",
                "data": {
                    "local_category_id": "cat-1",
                    "channel_id": "channel-1",
                    "platform_category_id": "platform-cat-1"
                },
                "expected_fields": ["success", "message"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/categories/ai-mapping",
                "data": {
                    "local_category_id": "cat-1",
                    "platform": "meituan"
                },
                "expected_fields": ["success", "data"]
            }
        ]
        
        return await self._test_endpoints(client, endpoints)
    
    async def test_platform_activities_endpoints(self, client: AsyncClient):
        """测试平台活动接口"""
        logger.info("🎁 测试平台活动接口...")
        
        endpoints = [
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/platform-activities",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/platform-activities/apply",
                "data": {
                    "activity_id": "activity-1",
                    "products": ["product-1", "product-2"],
                    "expected_sales": 100
                },
                "expected_fields": ["success", "message"]
            }
        ]
        
        return await self._test_endpoints(client, endpoints)
    
    async def test_channel_endpoints(self, client: AsyncClient):
        """测试渠道管理接口"""
        logger.info("📡 测试渠道管理接口...")
        
        endpoints = [
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/channels",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "GET",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/channels/configs",
                "expected_fields": ["success", "data"]
            },
            {
                "method": "POST",
                "url": f"/api/project/{self.project_id}/plugin/multi-channel-operations/channels/test-connection",
                "data": {
                    "platform_code": "meituan",
                    "api_credentials": {"api_key": "test-key"},
                    "platform_config": {}
                },
                "expected_fields": ["success", "message"]
            }
        ]
        
        return await self._test_endpoints(client, endpoints)
    
    async def _test_endpoints(self, client: AsyncClient, endpoints: List[Dict]) -> List[Dict]:
        """通用接口测试方法"""
        results = []
        
        for endpoint in endpoints:
            try:
                if endpoint["method"] == "GET":
                    response = await client.get(endpoint["url"], headers=self.headers)
                elif endpoint["method"] == "POST":
                    response = await client.post(
                        endpoint["url"], 
                        json=endpoint.get("data", {}), 
                        headers=self.headers
                    )
                elif endpoint["method"] == "PUT":
                    response = await client.put(
                        endpoint["url"], 
                        json=endpoint.get("data", {}), 
                        headers=self.headers
                    )
                elif endpoint["method"] == "DELETE":
                    response = await client.delete(endpoint["url"], headers=self.headers)
                
                result = {
                    "endpoint": endpoint["url"],
                    "method": endpoint["method"],
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 201],
                    "response_data": None,
                    "validation": {"passed": False, "errors": []}
                }
                
                if response.status_code in [200, 201]:
                    try:
                        data = response.json()
                        result["response_data"] = data
                        
                        # 验证响应字段
                        validation_errors = []
                        for field in endpoint.get("expected_fields", []):
                            if field not in data:
                                validation_errors.append(f"缺少字段: {field}")
                        
                        result["validation"]["passed"] = len(validation_errors) == 0
                        result["validation"]["errors"] = validation_errors
                        
                    except json.JSONDecodeError:
                        result["validation"]["errors"].append("响应不是有效的JSON")
                
                results.append(result)
                status = "✅" if result["success"] and result["validation"]["passed"] else "❌"
                logger.info(f"{status} {endpoint['method']} {endpoint['url']} - {response.status_code}")
                
            except Exception as e:
                results.append({
                    "endpoint": endpoint["url"],
                    "method": endpoint["method"],
                    "error": str(e),
                    "success": False
                })
                logger.error(f"❌ {endpoint['method']} {endpoint['url']} - {e}")
        
        return results

@pytest.mark.asyncio
async def test_multi_channel_plugin():
    """完整的插件测试"""
    logger.info("🚀 开始全渠道运营助手插件完整测试")
    
    tester = MultiChannelPluginTester()
    
    async with AsyncClient(base_url=tester.base_url) as client:
        # 测试各个模块
        dashboard_results = await tester.test_dashboard_endpoints(client)
        product_results = await tester.test_product_endpoints(client)
        ai_agent_results = await tester.test_ai_agent_endpoints(client)
        category_results = await tester.test_category_mapping_endpoints(client)
        activity_results = await tester.test_platform_activities_endpoints(client)
        channel_results = await tester.test_channel_endpoints(client)
        
        # 生成测试报告
        all_results = {
            "dashboard": dashboard_results,
            "products": product_results,
            "ai_agent": ai_agent_results,
            "categories": category_results,
            "activities": activity_results,
            "channels": channel_results
        }
        
        generate_test_report(all_results)
        
        return all_results

def generate_test_report(results: Dict[str, List[Dict]]):
    """生成详细测试报告"""
    logger.info("\n" + "="*100)
    logger.info("📊 全渠道运营助手插件完整测试报告")
    logger.info("="*100)
    
    total_tests = 0
    total_passed = 0
    
    for module_name, module_results in results.items():
        logger.info(f"\n📋 {module_name.upper()} 模块测试结果:")
        
        module_passed = 0
        for result in module_results:
            total_tests += 1
            success = result.get("success", False) and result.get("validation", {}).get("passed", False)
            if success:
                total_passed += 1
                module_passed += 1
            
            status = "✅" if success else "❌"
            logger.info(f"  {status} {result.get('method', 'GET')} {result.get('endpoint', 'Unknown')}")
            
            # 显示验证错误
            validation_errors = result.get("validation", {}).get("errors", [])
            if validation_errors:
                for error in validation_errors:
                    logger.info(f"    ⚠️ {error}")
        
        logger.info(f"  📈 模块通过率: {module_passed}/{len(module_results)} ({module_passed/len(module_results)*100:.1f}%)")
    
    # 总体统计
    success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    
    logger.info(f"\n📈 总体测试结果:")
    logger.info(f"  总测试数: {total_tests}")
    logger.info(f"  通过数: {total_passed}")
    logger.info(f"  成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        logger.info("🎉 插件测试优秀！")
    elif success_rate >= 70:
        logger.info("✅ 插件测试良好！")
    elif success_rate >= 50:
        logger.info("⚠️ 插件基本可用，需要优化")
    else:
        logger.info("❌ 插件存在严重问题，需要修复")
    
    logger.info("="*100)

if __name__ == "__main__":
    asyncio.run(test_multi_channel_plugin())
