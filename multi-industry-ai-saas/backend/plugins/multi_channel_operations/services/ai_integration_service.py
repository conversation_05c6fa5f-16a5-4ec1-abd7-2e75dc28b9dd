"""
全渠道运营AI智能体集成服务
复用现有AI助理系统，避免重复造轮子
"""

import uuid
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload

from services.ai.assistant_service import AIAssistantService
from services.ai.chat_service import AIChatService
from services.ai.system_integration_service import SystemAIIntegrationService
from schemas.ai.assistant import AIAssistantCreate, AIAssistantChatRequest
from models.ai.assistant import AIAssistant
from models.project import Project

logger = logging.getLogger(__name__)


class MultiChannelAIIntegrationService:
    """全渠道运营AI智能体集成服务"""
    
    # 全渠道运营专用助手名称
    ASSISTANT_NAME = "全渠道运营智能助手"
    ASSISTANT_DESCRIPTION = "专为全渠道运营设计的AI助手，支持商品管理、价格优化、库存同步、营销活动等功能"
    
    @staticmethod
    async def get_or_create_assistant(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> AIAssistant:
        """获取或创建全渠道运营专用AI助手"""
        try:
            # 查找现有的全渠道运营助手
            query = select(AIAssistant).where(
                AIAssistant.project_id == project_id,
                AIAssistant.name == MultiChannelAIIntegrationService.ASSISTANT_NAME,
                AIAssistant.status == "active"
            )
            result = await db.execute(query)
            assistant = result.scalars().first()
            
            if assistant:
                logger.info(f"找到现有全渠道运营助手: {assistant.id}")
                return assistant
            
            # 创建新的助手
            logger.info(f"为项目 {project_id} 创建全渠道运营助手")
            
            # 获取项目信息
            project_query = select(Project).where(Project.id == project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()
            
            if not project:
                raise ValueError(f"项目 {project_id} 不存在")
            
            # 定义全渠道运营助手的指令
            instructions = """你是一个专业的全渠道运营智能助手，具备以下核心能力：

## 🛍️ 商品管理能力
- 商品信息同步和映射
- 多平台商品上架/下架
- 商品规格和属性管理
- 商品图片生成和优化

## 💰 价格策略能力  
- 智能定价建议
- 竞品价格分析
- 动态价格调整
- 促销活动价格计算

## 📦 库存管理能力
- 多渠道库存同步
- 库存预警和补货建议
- 安全库存计算
- 库存分配优化

## 🎯 营销活动能力
- 平台活动分析和推荐
- 营销策略制定
- 活动效果预测
- ROI分析和优化

## 📊 数据分析能力
- 销售数据分析
- 渠道表现对比
- 趋势预测和洞察
- 运营报告生成

## 🔧 自动化能力
- 批量操作执行
- 规则引擎配置
- 异常处理和恢复
- 工作流程优化

请根据用户的具体需求，提供专业、准确、可执行的建议和操作方案。"""

            # 定义助手工具能力
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "sync_products",
                        "description": "同步商品到多个渠道",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "product_ids": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "要同步的商品ID列表"
                                },
                                "channel_ids": {
                                    "type": "array", 
                                    "items": {"type": "string"},
                                    "description": "目标渠道ID列表"
                                },
                                "sync_options": {
                                    "type": "object",
                                    "description": "同步选项配置"
                                }
                            },
                            "required": ["product_ids", "channel_ids"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "optimize_pricing",
                        "description": "优化商品定价策略",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "product_ids": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "要优化定价的商品ID列表"
                                },
                                "strategy": {
                                    "type": "string",
                                    "enum": ["competitive", "profit_max", "volume_max"],
                                    "description": "定价策略类型"
                                },
                                "constraints": {
                                    "type": "object",
                                    "description": "定价约束条件"
                                }
                            },
                            "required": ["product_ids", "strategy"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "analyze_performance",
                        "description": "分析渠道和商品表现",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "analysis_type": {
                                    "type": "string",
                                    "enum": ["channel", "product", "category", "overall"],
                                    "description": "分析类型"
                                },
                                "time_range": {
                                    "type": "object",
                                    "description": "时间范围"
                                },
                                "metrics": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "要分析的指标"
                                }
                            },
                            "required": ["analysis_type"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "generate_product_images",
                        "description": "AI生成商品图片",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "product_id": {
                                    "type": "string",
                                    "description": "商品ID"
                                },
                                "style": {
                                    "type": "string",
                                    "description": "图片风格"
                                },
                                "requirements": {
                                    "type": "object",
                                    "description": "图片要求"
                                }
                            },
                            "required": ["product_id"]
                        }
                    }
                }
            ]
            
            # 创建助手配置
            assistant_data = AIAssistantCreate(
                tenant_id=project.tenant_id,
                project_id=project.id,
                name=MultiChannelAIIntegrationService.ASSISTANT_NAME,
                description=MultiChannelAIIntegrationService.ASSISTANT_DESCRIPTION,
                instructions=instructions,
                model_id=None,  # 使用系统默认模型
                temperature=0.7,
                max_tokens=2000,
                status="active",
                is_public=False,
                assistant_config={
                    "tools": tools,
                    "plugin_type": "multi_channel_operations",
                    "version": "1.0.0",
                    "capabilities": [
                        "product_management",
                        "pricing_optimization", 
                        "inventory_management",
                        "marketing_automation",
                        "data_analysis",
                        "image_generation"
                    ]
                },
                welcome_message="👋 您好！我是全渠道运营智能助手，可以帮您管理商品、优化价格、分析数据等。请告诉我您需要什么帮助？"
            )
            
            # 使用现有AI助理服务创建助手
            assistant = await AIAssistantService.create_assistant(
                db=db,
                assistant_data=assistant_data,
                user_id=user_id
            )
            
            logger.info(f"成功创建全渠道运营助手: {assistant.id}")
            return assistant
            
        except Exception as e:
            logger.error(f"获取或创建全渠道运营助手失败: {e}")
            raise
    
    @staticmethod
    async def chat_with_assistant(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """与全渠道运营助手对话"""
        try:
            # 获取或创建助手
            assistant = await MultiChannelAIIntegrationService.get_or_create_assistant(
                db=db,
                project_id=project_id,
                user_id=user_id
            )
            
            # 构建聊天请求
            chat_request = AIAssistantChatRequest(
                assistant_id=assistant.id,
                message=message,
                conversation_id=conversation_id,
                user_id=user_id,
                context={
                    **(context or {}),
                    "plugin": "multi_channel_operations",
                    "project_id": str(project_id)
                },
                enable_knowledge_search=True,
                enable_tools=True,
                temperature=0.7,
                max_tokens=2000
            )
            
            # 使用现有AI助理服务进行对话
            response = await AIAssistantService.chat_with_assistant(
                db=db,
                chat_request=chat_request,
                user_id=user_id
            )
            
            return response
            
        except Exception as e:
            logger.error(f"与全渠道运营助手对话失败: {e}")
            raise
    
    @staticmethod
    async def get_assistant_capabilities(
        db: AsyncSession,
        project_id: uuid.UUID
    ) -> Dict[str, Any]:
        """获取助手能力信息"""
        try:
            # 获取系统AI集成配置
            integration = await SystemAIIntegrationService.get_effective_integration(
                db=db,
                project_id=project_id
            )
            
            return {
                "name": MultiChannelAIIntegrationService.ASSISTANT_NAME,
                "description": MultiChannelAIIntegrationService.ASSISTANT_DESCRIPTION,
                "capabilities": [
                    {
                        "name": "商品管理",
                        "description": "多平台商品同步、上架下架、规格管理",
                        "enabled": True
                    },
                    {
                        "name": "价格优化", 
                        "description": "智能定价、竞品分析、动态调价",
                        "enabled": True
                    },
                    {
                        "name": "库存管理",
                        "description": "多渠道库存同步、预警补货、分配优化", 
                        "enabled": True
                    },
                    {
                        "name": "营销自动化",
                        "description": "活动分析、策略制定、效果预测",
                        "enabled": True
                    },
                    {
                        "name": "数据分析",
                        "description": "销售分析、渠道对比、趋势预测",
                        "enabled": True
                    },
                    {
                        "name": "图片生成",
                        "description": "AI生成商品图片、优化视觉效果",
                        "enabled": integration and integration.get("vision_enabled", False)
                    }
                ],
                "ai_models": {
                    "chat_model": integration.get("default_chat_model_name") if integration else "系统默认",
                    "vision_model": integration.get("default_vision_model_name") if integration else "系统默认",
                    "chat_enabled": integration.get("chat_enabled", True) if integration else True,
                    "vision_enabled": integration.get("vision_enabled", False) if integration else False
                },
                "status": "active"
            }
            
        except Exception as e:
            logger.error(f"获取助手能力信息失败: {e}")
            return {
                "name": MultiChannelAIIntegrationService.ASSISTANT_NAME,
                "description": MultiChannelAIIntegrationService.ASSISTANT_DESCRIPTION,
                "capabilities": [],
                "ai_models": {},
                "status": "error",
                "error": str(e)
            }
