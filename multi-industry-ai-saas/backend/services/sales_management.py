from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy import select, func, or_, and_, cast, Date, desc, text, Float
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy.future import select
from sqlalchemy import update, delete
from fastapi import HTT<PERSON>Exception
from datetime import date
import logging
from sqlalchemy.dialects.postgresql import insert

from models.sales_management import (
    SalesChannel, PaymentMethod, ChannelPlatform, PlatformService, BusinessMode, 
    SalesChannelTemplate, ChannelTemplateInstance, sales_channel_payment_methods_table
)
from schemas.sales_management import (
    SalesChannelCreate, SalesChannelUpdate, SalesChannelResponse, PaymentMethodCreate, 
    PaymentMethodUpdate, ChannelPlatformCreate, ChannelPlatformUpdate, 
    PlatformServiceCreate, PlatformServiceUpdate, BusinessModeCreate, BusinessModeUpdate,
    SalesChannelTemplateCreate, SalesChannelTemplateUpdate
)
from models.project import Project
from models.store import Store
from models.sales_report import SalesReport, SalesReportChannel

logger = logging.getLogger(__name__)

# Import necessary modules for channel templates


class ChannelPlatformService:
    @staticmethod
    async def get_platforms(db: AsyncSession, skip: int, limit: int):
        result = await db.execute(select(ChannelPlatform).offset(skip).limit(limit))
        return result.scalars().all()

    @staticmethod
    async def create_platform(db: AsyncSession, platform_data: ChannelPlatformCreate):
        """创建渠道平台"""
        new_platform = ChannelPlatform(**platform_data.model_dump())
        db.add(new_platform)
        await db.commit()
        await db.refresh(new_platform)
        return new_platform

    @staticmethod
    async def update_platform(db: AsyncSession, platform_id: uuid.UUID, platform_data: ChannelPlatformUpdate):
        platform = await db.get(ChannelPlatform, platform_id)
        if not platform:
            raise HTTPException(status_code=404, detail="Platform not found")
        update_data = platform_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(platform, key, value)
        await db.commit()
        await db.refresh(platform)
        return platform

    @staticmethod
    async def delete_platform(db: AsyncSession, platform_id: uuid.UUID):
        platform = await db.get(ChannelPlatform, platform_id)
        if not platform:
            raise HTTPException(status_code=404, detail="Platform not found")
        await db.delete(platform)
        await db.commit()
        return


class PlatformServiceService:
    @staticmethod
    async def get_services_by_platform(db: AsyncSession, platform_id: uuid.UUID):
        result = await db.execute(
            select(PlatformService)
            .options(selectinload(PlatformService.platform))
            .where(PlatformService.platform_id == platform_id)
        )
        return result.scalars().all()

    @staticmethod
    async def get_all_services(db: AsyncSession, skip: int = 0, limit: int = 100):
        """获取所有服务类型，带关联的平台信息"""
        result = await db.execute(
            select(PlatformService)
            .options(selectinload(PlatformService.platform))
            .order_by(PlatformService.name)
            .offset(skip).limit(limit)
        )
        return result.scalars().all()

    @staticmethod
    async def create_service(db: AsyncSession, service_in: PlatformServiceCreate):
        """创建新的服务类型"""
        db_service = PlatformService(**service_in.model_dump())
        db.add(db_service)
        await db.commit()
        
        # 重新查询以加载关联对象
        result = await db.execute(
            select(PlatformService)
            .options(selectinload(PlatformService.platform))
            .where(PlatformService.id == db_service.id)
        )
        return result.scalars().first()

    @staticmethod
    async def update_service(db: AsyncSession, service_id: uuid.UUID, service_in: PlatformServiceUpdate):
        """更新服务类型"""
        result = await db.execute(select(PlatformService).where(PlatformService.id == service_id))
        db_service = result.scalars().first()

        if not db_service:
            return None

        update_data = service_in.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_service, key, value)
        
        await db.commit()

        # 重新查询以加载关联对象
        result = await db.execute(
            select(PlatformService)
            .options(selectinload(PlatformService.platform))
            .where(PlatformService.id == service_id)
        )
        return result.scalars().first()

    @staticmethod
    async def delete_service(db: AsyncSession, service_id: uuid.UUID):
        """删除服务类型"""
        # 查询时预加载关联对象
        result = await db.execute(
            select(PlatformService)
            .options(selectinload(PlatformService.platform))
            .where(PlatformService.id == service_id)
        )
        service = result.scalars().first()
        if service:
            await db.delete(service)
            await db.commit()
        return service


class BusinessModeService:
    @staticmethod
    async def get_modes_by_project(db: AsyncSession, project_id: uuid.UUID):
        result = await db.execute(
            select(BusinessMode).where(BusinessMode.project_id == project_id)
        )
        return result.scalars().all()

    @staticmethod
    async def create_mode(db: AsyncSession, project_id: uuid.UUID, mode_data: BusinessModeCreate):
        new_mode = BusinessMode(project_id=project_id, **mode_data.model_dump())
        db.add(new_mode)
        await db.commit()
        await db.refresh(new_mode)
        return new_mode

    @staticmethod
    async def update_mode(db: AsyncSession, mode_id: uuid.UUID, project_id: uuid.UUID, mode_data: BusinessModeUpdate):
        mode = await db.get(BusinessMode, mode_id)
        if not mode or mode.project_id != project_id:
            raise HTTPException(status_code=404, detail="Business mode not found")
        update_data = mode_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(mode, key, value)
        await db.commit()
        await db.refresh(mode)
        return mode

    @staticmethod
    async def delete_mode(db: AsyncSession, mode_id: uuid.UUID, project_id: uuid.UUID):
        mode = await db.get(BusinessMode, mode_id)
        if not mode or mode.project_id != project_id:
            raise HTTPException(status_code=404, detail="Business mode not found")
        await db.delete(mode)
        await db.commit()
        return


class SalesChannelService:
    @staticmethod
    async def create_sales_channel(
        db: AsyncSession,
        project_id: uuid.UUID,
        channel_data: SalesChannelCreate,
    ) -> SalesChannel:
        """创建销售渠道实例"""
        channel_dict = channel_data.model_dump(exclude={'payment_method_ids'})
        payment_method_ids = channel_data.payment_method_ids or []

        db_channel = SalesChannel(
            project_id=project_id,
            **channel_dict
        )

        if payment_method_ids:
            payment_methods_query = await db.execute(
                select(PaymentMethod).where(PaymentMethod.id.in_(payment_method_ids))
            )
            db_channel.payment_methods = payment_methods_query.scalars().all()

        db.add(db_channel)
        await db.commit()

        # Re-fetch with relationships to prevent lazy-load errors
        result = await db.execute(
            select(SalesChannel)
            .options(
                selectinload(SalesChannel.service).selectinload(PlatformService.platform),
                selectinload(SalesChannel.business_mode),
                selectinload(SalesChannel.payment_methods),
                joinedload(SalesChannel.store)
            )
            .where(SalesChannel.id == db_channel.id)
        )
        return result.scalar_one()

    @staticmethod
    async def get_sales_channels(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[SalesChannel]:
        """获取销售渠道列表"""
        query = select(SalesChannel).where(SalesChannel.project_id == project_id).options(
            selectinload(SalesChannel.service).selectinload(PlatformService.platform),
            selectinload(SalesChannel.business_mode),
            selectinload(SalesChannel.payment_methods),
            joinedload(SalesChannel.store)
        )
        
        # 应用过滤条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SalesChannel.custom_name.ilike(search_term),
                    SalesChannel.description.ilike(search_term)
                )
            )
        
        if type:
            # This filter is based on an old model field, needs to be adapted.
            # For now, we query based on the platform's nature.
            query = query.join(SalesChannel.service).join(PlatformService.platform).where(ChannelPlatform.nature == type)

        if is_active is not None:
            query = query.where(SalesChannel.is_active == is_active)
        
        # 应用排序和分页
        query = query.order_by(SalesChannel.custom_name).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().unique().all()

    @staticmethod
    async def get_channel_overview(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取渠道概览数据，包含全局和单店维度的丰富分析.
        """
        # 基础查询: 已审核的销售报告
        base_query = (
            select(SalesReport)
            .where(
                SalesReport.project_id == project_id,
                SalesReport.status == 'approved' # 只统计已审核的
            )
        )
        if store_id:
            base_query = base_query.where(SalesReport.store_id == store_id)
        if start_date:
            base_query = base_query.where(cast(SalesReport.report_date, Date) >= date.fromisoformat(start_date))
        if end_date:
            base_query = base_query.where(cast(SalesReport.report_date, Date) <= date.fromisoformat(end_date))

        # 执行基础查询获取报告
        reports_result = await db.execute(base_query)
        reports = reports_result.scalars().all()
        report_ids = [r.id for r in reports]

        if not report_ids:
            return {
                "total_sales": 0, "total_orders": 0, "total_channels": 0, "active_channels": 0,
                "platform_sales": [], "daily_trend": [], "top_stores_by_sales": [],
                "top_channel_instances_by_sales": [], "payment_method_summary": [], "channel_instance_summary": []
            }

        # --- 核心指标 ---
        total_sales = sum(r.total_sales for r in reports)
        total_orders = sum(r.total_orders for r in reports)

        # --- 渠道实例指标 ---
        total_channels_q = await db.execute(select(func.count(SalesChannel.id)).where(SalesChannel.project_id == project_id))
        active_channels_q = await db.execute(select(func.count(SalesChannel.id)).where(SalesChannel.project_id == project_id, SalesChannel.is_active == True))
        total_channels = total_channels_q.scalar_one()
        active_channels = active_channels_q.scalar_one()

        # --- 日期趋势 ---
        daily_trend_query = (
            select(
                cast(SalesReport.report_date, Date).label("date"),
                func.sum(SalesReport.total_sales).label("sales")
            )
            .where(SalesReport.id.in_(report_ids))
            .group_by(cast(SalesReport.report_date, Date))
            .order_by(cast(SalesReport.report_date, Date))
        )
        daily_trend_result = await db.execute(daily_trend_query)
        daily_trend = [{"date": str(r.date), "sales": r.sales or 0} for r in daily_trend_result.all()]

        # --- 平台销售分布 ---
        platform_sales_query = (
            select(
                ChannelPlatform.name.label("platform_name"),
                func.sum(SalesReportChannel.total_sales).label("sales")
            )
            .join(SalesReportChannel, SalesReportChannel.sales_report_id.in_(report_ids))
            .join(SalesChannel, SalesChannel.id == SalesReportChannel.sales_channel_id)
            .join(PlatformService, PlatformService.id == SalesChannel.service_id)
            .join(ChannelPlatform, ChannelPlatform.id == PlatformService.platform_id)
            .group_by(ChannelPlatform.name)
        )
        platform_sales_result = await db.execute(platform_sales_query)
        platform_sales = [{"platform_name": r.platform_name, "sales": r.sales or 0} for r in platform_sales_result.all()]

        # --- 新增分析指标 ---
        result_payload = {}
        
        # 渠道实例相关的查询
        channel_details_query = (
            select(
                SalesChannel.custom_name,
                func.sum(SalesReportChannel.total_sales).label("total_sales"),
                func.sum(SalesReportChannel.total_orders).label("total_orders")
            )
            .select_from(SalesReportChannel)
            .join(SalesChannel, SalesChannel.id == SalesReportChannel.sales_channel_id)
            .where(SalesReportChannel.sales_report_id.in_(report_ids))
            .group_by(SalesChannel.custom_name)
            .order_by(desc("total_sales"))
        )
        channel_details_result = await db.execute(channel_details_query)
        channel_summary = [{"name": r.custom_name, "sales": r.total_sales or 0, "orders": r.total_orders or 0} for r in channel_details_result.all()]

        # 支付方式相关的查询 (从JSON中提取)
        # This is a complex query on JSON field, might need optimization for production
        payment_details_query = (
            select(
                text("jsonb_array_elements(payment_methods_details)->>'payment_method_name' as name"),
                func.sum(cast(text("jsonb_array_elements(payment_methods_details)->>'amount'"), Float)).label("total_amount")
            )
            .select_from(SalesReportChannel)
            .where(SalesReportChannel.sales_report_id.in_(report_ids))
            .group_by(text("name"))
        )
        payment_details_result = await db.execute(payment_details_query)
        payment_method_summary = [{"name": r.name, "amount": r.total_amount or 0} for r in payment_details_result.all()]

        if store_id:
            # 单店视角
            result_payload["channel_instance_summary"] = channel_summary
        else:
            # 全局视角
            result_payload["top_channel_instances_by_sales"] = channel_summary

            # 门店销售排行
            store_sales_query = (
                select(
                    Store.name.label("store_name"),
                    func.sum(SalesReport.total_sales).label("total_sales")
                )
                .join(Store, Store.id == SalesReport.store_id)
                .where(SalesReport.id.in_(report_ids))
                .group_by(Store.name)
                .order_by(desc("total_sales"))
                .limit(10)
            )
            store_sales_result = await db.execute(store_sales_query)
            result_payload["top_stores_by_sales"] = [{"name": r.store_name, "sales": r.total_sales or 0} for r in store_sales_result.all()]
        
        result_payload["payment_method_summary"] = payment_method_summary

        return {
            "total_sales": total_sales,
            "total_orders": total_orders,
            "total_channels": total_channels,
            "active_channels": active_channels,
            "platform_sales": platform_sales,
            "daily_trend": daily_trend,
            **result_payload
        }

    @staticmethod
    async def count_sales_channels(
        db: AsyncSession,
        project_id: uuid.UUID,
        search: Optional[str] = None,
        type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """计算销售渠道总数"""
        query = select(func.count(SalesChannel.id)).where(SalesChannel.project_id == project_id)
        
        # 应用过滤条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SalesChannel.custom_name.ilike(search_term),
                    SalesChannel.description.ilike(search_term)
                )
            )
        
        if type:
            query = query.join(SalesChannel.service).join(PlatformService.platform).where(ChannelPlatform.nature == type)

        if is_active is not None:
            query = query.where(SalesChannel.is_active == is_active)
        
        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_sales_channel_by_id(
        db: AsyncSession,
        channel_id: uuid.UUID
    ) -> Optional[SalesChannel]:
        """根据ID获取销售渠道"""
        stmt = (
            select(SalesChannel)
            .options(
                selectinload(SalesChannel.service).selectinload(PlatformService.platform),
                selectinload(SalesChannel.business_mode),
                selectinload(SalesChannel.payment_methods),
                joinedload(SalesChannel.store)
            )
            .where(SalesChannel.id == channel_id)
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def update_sales_channel(
        db: AsyncSession,
        channel_id: uuid.UUID,
        channel_data: SalesChannelUpdate
    ) -> Optional[SalesChannel]:
        """更新销售渠道"""
        db_channel = await db.get(SalesChannel, channel_id)
        
        if not db_channel:
            return None
        
        update_data = channel_data.model_dump(exclude_unset=True)
        payment_method_ids = update_data.pop('payment_method_ids', None)

        # 更新字段
        for key, value in update_data.items():
            if hasattr(db_channel, key):
                setattr(db_channel, key, value)
        
        if payment_method_ids is not None:
            # Fetch fresh payment method objects from the DB
            payment_methods_result = await db.execute(
                select(PaymentMethod).where(PaymentMethod.id.in_(payment_method_ids))
            )
            db_channel.payment_methods = payment_methods_result.scalars().all()

        await db.commit()
        
        # Re-fetch with relationships to prevent lazy-load errors in response
        return await SalesChannelService.get_sales_channel_by_id(db, channel_id)

    @staticmethod
    async def delete_sales_channel(
        db: AsyncSession,
        channel_id: uuid.UUID
    ) -> bool:
        """删除销售渠道"""
        db_channel = await db.get(SalesChannel, channel_id)
        if db_channel:
            await db.delete(db_channel)
            await db.commit()
            return True
        return False


class PaymentMethodService:
    @staticmethod
    async def get_payment_methods(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[PaymentMethod]:
        """获取支付方式列表"""
        query = select(PaymentMethod).where(PaymentMethod.project_id == project_id).order_by(PaymentMethod.name).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_payment_methods(db: AsyncSession, project_id: uuid.UUID) -> int:
        """计算支付方式总数"""
        query = select(func.count(PaymentMethod.id)).where(PaymentMethod.project_id == project_id)
        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_payment_method(db: AsyncSession, method_id: uuid.UUID) -> Optional[PaymentMethod]:
        """根据ID获取支付方式"""
        stmt = select(PaymentMethod).where(PaymentMethod.id == method_id)
        result = await db.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def create_payment_method(db: AsyncSession, project_id: uuid.UUID, method_in: PaymentMethodCreate) -> PaymentMethod:
        """创建支付方式"""
        db_method = PaymentMethod(project_id=project_id, **method_in.model_dump())
        db.add(db_method)
        await db.commit()
        await db.refresh(db_method)
        return db_method

    @staticmethod
    async def update_payment_method(
        db: AsyncSession,
        method_id: uuid.UUID,
        method_in: Dict[str, Any]
    ) -> Optional[PaymentMethod]:
        """更新支付方式"""
        # 使用 aio-libs/sqlalchemy 的 update() 方式，效率更高
        update_stmt = (
            update(PaymentMethod)
            .where(PaymentMethod.id == method_id)
            .values(method_in)
        )
        await db.execute(update_stmt)
        await db.commit()
        
        # 返回更新后的对象
        updated_method = await db.get(PaymentMethod, method_id)
        return updated_method

    @staticmethod
    async def delete_payment_method(db: AsyncSession, method_id: uuid.UUID) -> bool:
        """删除支付方式"""
        db_method = await PaymentMethodService.get_payment_method(db, method_id)
        if not db_method:
            return False
        await db.delete(db_method)
        await db.commit()
        return True


# --- Service for Sales Channel Templates ---

class SalesChannelTemplateService:
    async def create_template(self, db: AsyncSession, *, project_id: uuid.UUID, payload: SalesChannelTemplateCreate) -> Optional[SalesChannelTemplate]:
        # Create the template object
        template_data = payload.model_dump(exclude={"instances"})
        db_obj = SalesChannelTemplate(**template_data, project_id=project_id)
        
        # Create and associate instance templates
        if payload.instances:
            for instance_in in payload.instances:
                instance_data = instance_in.model_dump(exclude={'payment_method_ids'})
                payment_method_ids = instance_in.payment_method_ids or []
                
                db_instance = ChannelTemplateInstance(**instance_data)
                
                if payment_method_ids:
                    # This part could be slow, but for creation it's acceptable.
                    payment_methods_query = await db.execute(
                        select(PaymentMethod).where(PaymentMethod.id.in_(payment_method_ids))
                    )
                    db_instance.payment_methods = payment_methods_query.scalars().all()
                
                db_obj.instances.append(db_instance)

        db.add(db_obj)
        await db.commit()
        
        # Re-fetch the created object with all necessary relationships eagerly loaded.
        result = await db.execute(
            select(SalesChannelTemplate)
            .options(
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.service).selectinload(PlatformService.platform),
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.business_mode),
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.payment_methods)
            )
            .where(SalesChannelTemplate.id == db_obj.id)
        )
        return result.scalar_one_or_none()

    async def get(self, db: AsyncSession, *, project_id: uuid.UUID, id: uuid.UUID) -> Optional[SalesChannelTemplate]:
        """Fetch a single template with all relationships."""
        result = await db.execute(
            select(SalesChannelTemplate)
            .options(
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.service).selectinload(PlatformService.platform),
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.business_mode),
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.payment_methods)
            )
            .where(SalesChannelTemplate.project_id == project_id, SalesChannelTemplate.id == id)
        )
        return result.scalar_one_or_none()

    async def get_multi(self, db: AsyncSession, *, project_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[SalesChannelTemplate]:
        """Fetch multiple templates with relationships."""
        result = await db.execute(
            select(SalesChannelTemplate)
            .options(
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.service).selectinload(PlatformService.platform),
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.business_mode),
                selectinload(SalesChannelTemplate.instances).selectinload(ChannelTemplateInstance.payment_methods)
            )
            .where(SalesChannelTemplate.project_id == project_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update(self, db: AsyncSession, *, db_obj: SalesChannelTemplate, obj_in: SalesChannelTemplateUpdate) -> SalesChannelTemplate:
        # Update template's own fields
        update_data = obj_in.model_dump(exclude_unset=True, exclude={'instances'})
        for field, value in update_data.items():
            setattr(db_obj, field, value)

        # Handle instances
        if obj_in.instances is not None:
            # Create a map of existing instances for quick access
            existing_instances_map = {str(inst.id): inst for inst in db_obj.instances}
            incoming_instance_ids = {str(inst.id) for inst in obj_in.instances if inst.id}

            # Delete instances that are no longer in the list
            for inst_id, inst_obj in existing_instances_map.items():
                if inst_id not in incoming_instance_ids:
                    await db.delete(inst_obj)

            # Update existing instances and create new ones
            for instance_in in obj_in.instances:
                if instance_in.id: # Update existing instance
                    db_instance = existing_instances_map.get(str(instance_in.id))
                    if db_instance:
                        inst_update_data = instance_in.model_dump(exclude_unset=True, exclude={'payment_method_ids', 'id'})
                        for field, value in inst_update_data.items():
                            setattr(db_instance, field, value)
                        
                        if instance_in.payment_method_ids is not None:
                            payment_methods = await db.execute(
                                select(PaymentMethod).where(PaymentMethod.id.in_(instance_in.payment_method_ids))
                            )
                            db_instance.payment_methods = payment_methods.scalars().all()
                else: # Create new instance
                    instance_data = instance_in.model_dump(exclude={'payment_method_ids', 'id'})
                    new_db_instance = ChannelTemplateInstance(**instance_data, template_id=db_obj.id)
                    if instance_in.payment_method_ids:
                         payment_methods = await db.execute(
                            select(PaymentMethod).where(PaymentMethod.id.in_(instance_in.payment_method_ids))
                        )
                         new_db_instance.payment_methods = payment_methods.scalars().all()
                    db.add(new_db_instance)

        await db.commit()
        await db.refresh(db_obj)
        return await self.get(db, project_id=db_obj.project_id, id=db_obj.id)

    async def remove(self, db: AsyncSession, *, id: uuid.UUID) -> Optional[SalesChannelTemplate]:
        result = await db.execute(select(SalesChannelTemplate).where(SalesChannelTemplate.id == id))
        db_obj = result.scalars().first()
        if db_obj:
            await db.delete(db_obj)
            await db.commit()
        return db_obj

    async def batch_create_from_template(self, db: AsyncSession, project_id: uuid.UUID, template_id: uuid.UUID, store_ids: List[uuid.UUID]):
        # 1. Get the template with its instances and nested relationships
        template = await self.get(db=db, project_id=project_id, id=template_id)
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")

        # CRITICAL: Decouple ORM objects from the processing loop.
        # Extract all necessary data into a plain list of dicts *before* any other async operations.
        instance_definitions = []
        try:
            template_name_str = template.name  # Cache template name
            for instance in template.instances:
                instance_definitions.append({
                    "service_id": instance.service_id,
                    "service_name": instance.service.name,  # Cache service name
                    "business_mode_id": instance.business_mode_id,
                    "payment_method_ids": [pm.id for pm in instance.payment_methods]
                })
        except Exception as e:
            if "greenlet" in str(e).lower():
                raise HTTPException(status_code=500, detail="Failed to read template data structure. A lazy-loading issue occurred. Please verify template configuration.")
            raise e

        # 2. Get the store objects
        stores_result = await db.execute(select(Store).where(Store.id.in_(store_ids), Store.project_id == project_id))
        stores = stores_result.scalars().all()
        store_map = {s.id: s for s in stores}

        if len(stores) != len(store_ids):
            found_ids = set(store_map.keys())
            not_found_ids = set(store_ids) - found_ids
            error_detail = f"One or more stores not found or do not belong to this project: {not_found_ids}"
            raise HTTPException(status_code=404, detail=error_detail)

        # 3. First Pass: Create SalesChannel objects
        new_channels_to_create = []
        channels_with_payment_ids = []
        for store_id in store_ids:
            store = store_map.get(store_id)
            if not store: continue
            
            store_name_str = store.name

            for instance_def in instance_definitions:
                service_id = instance_def['service_id']
                business_mode_id = instance_def['business_mode_id']
                payment_method_ids = instance_def['payment_method_ids']
                service_name = instance_def['service_name']

                # This check must be awaited, so we do it before creating the object
                existing_channel_check = await db.execute(
                    select(SalesChannel.id).where(
                        and_(
                            SalesChannel.store_id == store_id,
                            SalesChannel.service_id == service_id,
                            SalesChannel.business_mode_id == business_mode_id
                        )
                    ).limit(1)
                )
                if existing_channel_check.scalars().first():
                    continue

                new_channel = SalesChannel(
                    project_id=project_id,
                    store_id=store_id,
                    custom_name=f"{store_name_str} - {service_name}",
                    service_id=service_id,
                    business_mode_id=business_mode_id,
                    is_active=True,
                    description=f"Created from template: {template_name_str}",
                )
                new_channels_to_create.append(new_channel)
                # Store the object and its future associations
                channels_with_payment_ids.append({"channel": new_channel, "payment_ids": payment_method_ids})
        
        # Commit all new channels first to get their IDs
        if not new_channels_to_create:
            return {"created_count": 0}

        db.add_all(new_channels_to_create)
        await db.commit()

        # 4. Second Pass: Create associations in the join table
        association_list = []
        for item in channels_with_payment_ids:
            channel_id = item['channel'].id # Now this ID is persistent and safe to use
            for pm_id in item['payment_ids']:
                association_list.append({
                    "sales_channel_id": channel_id,
                    "payment_method_id": pm_id
                })

        if association_list:
            # Use a bulk insert for efficiency
            stmt = insert(sales_channel_payment_methods_table).values(association_list)
            await db.execute(stmt)
            await db.commit()
        
        return {"created_count": len(new_channels_to_create)}

sales_channel_template_service = SalesChannelTemplateService()
