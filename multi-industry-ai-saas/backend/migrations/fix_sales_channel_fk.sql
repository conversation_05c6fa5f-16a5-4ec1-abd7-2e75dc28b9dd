-- This script fixes a broken foreign key constraint on the sales_channel_payment_methods table
-- and removes the obsolete backup tables from the `sales_channel_refactor` migration.

BEGIN;

-- Step 1: Drop the obsolete backup tables and all dependent objects (like wrong FK constraints).
-- Using CASCADE is necessary because other tables might still incorrectly reference these backup tables.
DROP TABLE IF EXISTS public.sales_channels_backup CASCADE;
DROP TABLE IF EXISTS public.payment_methods_backup CASCADE;

-- Step 2: Now that the old constraints are gone (due to CASCADE), we can safely add the correct one.
-- It's good practice to ensure it doesn't exist before creating it, though CASCADE should have handled it.
ALTER TABLE public.sales_channel_payment_methods
DROP CONSTRAINT IF EXISTS sales_channel_payment_methods_sales_channel_id_fkey;

ALTER TABLE public.sales_channel_payment_methods
ADD CONSTRAINT sales_channel_payment_methods_sales_channel_id_fkey
FOREIGN KEY (sales_channel_id) REFERENCES public.sales_channels(id) ON DELETE CASCADE;

-- Also, let's fix the financial_reconciliations FK that was also pointing to the backup table.
ALTER TABLE public.financial_reconciliations
DROP CONSTRAINT IF EXISTS financial_reconciliations_channel_id_fkey;

ALTER TABLE public.financial_reconciliations
ADD CONSTRAINT financial_reconciliations_channel_id_fkey
FOREIGN KEY (channel_id) REFERENCES public.sales_channels(id) ON DELETE SET NULL;

COMMIT;

-- After running this script, the foreign key relationships should be corrected,
-- obsolete tables will be removed, and the IntegrityError should be resolved. 