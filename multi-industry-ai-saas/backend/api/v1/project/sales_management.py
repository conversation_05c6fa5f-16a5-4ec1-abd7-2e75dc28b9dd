from typing import Optional, List, Any
import uuid
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from datetime import date

from api.deps import get_db, get_current_user, get_current_project
from models.user import User
from models.project import Project
from schemas.sales_management import (
    SalesChannelCreate,
    SalesChannelUpdate,
    SalesChannelResponse,
    SalesChannelListResponse,
    PaymentMethodCreate,
    PaymentMethodUpdate,
    PaymentMethodResponse,
    PaymentMethodListResponse,
    ChannelPlatformCreate,
    ChannelPlatformUpdate,
    ChannelPlatformResponse,
    PlatformServiceCreate,
    PlatformServiceUpdate,
    PlatformServiceResponse,
    BusinessModeCreate,
    BusinessModeUpdate,
    BusinessModeResponse,
    ChannelOverviewResponse,
    SalesChannelTemplateCreate,
    SalesChannelTemplateUpdate,
    SalesChannelTemplateResponse,
    SalesChannelTemplateListResponse,
)
from services.sales_management import (
    SalesChannelService,
    PaymentMethodService,
    ChannelPlatformService,
    PlatformServiceService,
    BusinessModeService,
    sales_channel_template_service
)
from pydantic import BaseModel, Field

router = APIRouter()

# --- 基础定义 API ---

@router.get("/platforms", response_model=List[ChannelPlatformResponse])
async def get_platforms(
    skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_db)
):
    """获取所有渠道平台"""
    platforms = await ChannelPlatformService.get_platforms(db, skip, limit)
    return platforms

@router.post("/platforms", response_model=ChannelPlatformResponse, status_code=status.HTTP_201_CREATED)
async def create_platform(
    platform_data: ChannelPlatformCreate, db: AsyncSession = Depends(get_db)
):
    """创建渠道平台 (通常由超级管理员操作)"""
    return await ChannelPlatformService.create_platform(db, platform_data)

@router.put("/platforms/{platform_id}", response_model=ChannelPlatformResponse)
async def update_platform(
    platform_id: uuid.UUID,
    platform_data: ChannelPlatformUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新渠道平台"""
    return await ChannelPlatformService.update_platform(db, platform_id, platform_data)

@router.delete("/platforms/{platform_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_platform(
    platform_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """删除渠道平台"""
    await ChannelPlatformService.delete_platform(db, platform_id)
    return

@router.get("/platforms/{platform_id}/services", response_model=List[PlatformServiceResponse])
async def get_services_by_platform(
    platform_id: uuid.UUID, db: AsyncSession = Depends(get_db)
):
    """获取指定平台下的所有服务类型"""
    services = await PlatformServiceService.get_services_by_platform(db, platform_id)
    return services

@router.get("/services", response_model=List[PlatformServiceResponse])
async def get_all_services(
    skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_db)
):
    """获取所有服务类型"""
    services = await PlatformServiceService.get_all_services(db, skip, limit)
    return services

@router.post("/services", response_model=PlatformServiceResponse, status_code=status.HTTP_201_CREATED)
async def create_service(
    service_data: PlatformServiceCreate, db: AsyncSession = Depends(get_db)
):
    """为平台创建服务类型 (通常由超级管理员操作)"""
    return await PlatformServiceService.create_service(db, service_data)

@router.put("/services/{service_id}", response_model=PlatformServiceResponse)
async def update_service(
    service_id: uuid.UUID,
    service_data: PlatformServiceUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新平台服务"""
    return await PlatformServiceService.update_service(db, service_id, service_data)

@router.delete("/services/{service_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_service(
    service_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """删除平台服务"""
    await PlatformServiceService.delete_service(db, service_id)
    return

@router.get("/business-modes", response_model=List[BusinessModeResponse])
async def get_business_modes(
    project: Project = Depends(get_current_project), db: AsyncSession = Depends(get_db)
):
    """获取项目的所有业务模式"""
    return await BusinessModeService.get_modes_by_project(db, project.id)

@router.post("/business-modes", response_model=BusinessModeResponse, status_code=status.HTTP_201_CREATED)
async def create_business_mode(
    mode_data: BusinessModeCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """为项目创建业务模式"""
    return await BusinessModeService.create_mode(db, project.id, mode_data)

@router.put("/business-modes/{mode_id}", response_model=BusinessModeResponse)
async def update_business_mode(
    mode_id: uuid.UUID,
    mode_data: BusinessModeUpdate,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
):
    """更新业务模式"""
    return await BusinessModeService.update_mode(db, mode_id, project.id, mode_data)

@router.delete("/business-modes/{mode_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_business_mode(
    mode_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
):
    """删除业务模式"""
    await BusinessModeService.delete_mode(db, mode_id, project.id)
    return


# --- 销售渠道实例 API (重构后) ---

@router.get("/channels/overview", response_model=ChannelOverviewResponse)
async def get_sales_channels_overview(
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project),
    store_id: Optional[uuid.UUID] = Query(None, description="按门店ID筛选"),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
):
    """
    Get an overview of sales channels performance.
    """
    overview_data = await SalesChannelService.get_channel_overview(
        db=db, 
        project_id=project.id, 
        store_id=store_id,
        start_date=start_date.isoformat() if start_date else None,
        end_date=end_date.isoformat() if end_date else None
    )
    return overview_data

@router.get("/channels", response_model=SalesChannelListResponse)
async def get_sales_channels(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取销售渠道实例列表"""
    channels = await SalesChannelService.get_sales_channels(db, project.id, skip, limit)
    total = await SalesChannelService.count_sales_channels(db, project.id)
    return {"items": channels, "total": total}

@router.post("/channels", response_model=SalesChannelResponse, status_code=status.HTTP_201_CREATED)
async def create_sales_channel(
    channel_data: SalesChannelCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建销售渠道实例"""
    return await SalesChannelService.create_sales_channel(db, project.id, channel_data)

@router.get("/channels/{channel_id}", response_model=SalesChannelResponse)
async def get_sales_channel(
    channel_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取销售渠道实例详情"""
    channel = await SalesChannelService.get_sales_channel_by_id(db, channel_id)
    if not channel or channel.project_id != project.id:
        raise HTTPException(status_code=404, detail="销售渠道不存在")
    return channel

@router.put("/channels/{channel_id}", response_model=SalesChannelResponse)
async def update_sales_channel(
    channel_data: SalesChannelUpdate,
    channel_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新销售渠道实例"""
    channel = await SalesChannelService.get_sales_channel_by_id(db, channel_id)
    if not channel or channel.project_id != project.id:
        raise HTTPException(status_code=404, detail="销售渠道不存在")
    
    update_dict = channel_data.dict(exclude_unset=True)
    updated_channel = await SalesChannelService.update_sales_channel(db, channel_id, update_dict)
    return updated_channel

@router.delete("/channels/{channel_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_sales_channel(
    channel_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除销售渠道实例"""
    channel = await SalesChannelService.get_sales_channel_by_id(db, channel_id)
    if not channel or channel.project_id != project.id:
        raise HTTPException(status_code=404, detail="销售渠道不存在")
    
    await SalesChannelService.delete_sales_channel(db, channel_id)
    return

# --- 支付方式 API (兼容新架构) ---

@router.get("/payment-methods", response_model=PaymentMethodListResponse)
async def get_payment_methods(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    store_id: Optional[uuid.UUID] = Query(None, description="按门店ID过滤"),
    for_recharge: Optional[bool] = Query(None, description="过滤用于充值的支付方式"),
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
):
    """获取支付方式列表"""
    payment_methods = await PaymentMethodService.get_payment_methods(
        db, project.id, skip, limit, store_id, for_recharge
    )
    total = await PaymentMethodService.count_payment_methods(
        db, project.id, store_id, for_recharge
    )
    return {
        "items": payment_methods,
        "total": total,
    }

@router.post("/payment-methods", response_model=PaymentMethodResponse, status_code=status.HTTP_201_CREATED)
async def create_payment_method(
    payment_method_data: PaymentMethodCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建支付方式"""
    return await PaymentMethodService.create_payment_method(db, project.id, payment_method_data)

@router.get("/payment-methods/{payment_method_id}", response_model=PaymentMethodResponse)
async def get_payment_method(
    payment_method_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取支付方式详情"""
    payment_method = await PaymentMethodService.get_payment_method_by_id(db, payment_method_id)
    if not payment_method or payment_method.project_id != project.id:
        raise HTTPException(status_code=404, detail="支付方式不存在")
    return payment_method

@router.put("/payment-methods/{payment_method_id}", response_model=PaymentMethodResponse)
async def update_payment_method(
    payment_method_data: PaymentMethodUpdate,
    payment_method_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新支付方式"""
    payment_method = await PaymentMethodService.get_payment_method(db, payment_method_id)
    if not payment_method or payment_method.project_id != project.id:
        raise HTTPException(status_code=404, detail="支付方式不存在")
    
    update_dict = payment_method_data.model_dump(exclude_unset=True)
    return await PaymentMethodService.update_payment_method(db, payment_method_id, update_dict)

@router.delete("/payment-methods/{payment_method_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_payment_method(
    payment_method_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除支付方式"""
    payment_method = await PaymentMethodService.get_payment_method(db, payment_method_id)
    if not payment_method or payment_method.project_id != project.id:
        raise HTTPException(status_code=404, detail="支付方式不存在")
    
    await PaymentMethodService.delete_payment_method(db, payment_method_id)
    return

# --- 销售渠道模板 API (重构后) ---

@router.post("/templates", response_model=SalesChannelTemplateResponse, status_code=status.HTTP_201_CREATED)
async def create_sales_channel_template(
    template_in: SalesChannelTemplateCreate,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
) -> Any:
    """
    Create new sales channel template.
    """
    template = await sales_channel_template_service.create_template(
        db, payload=template_in, project_id=project.id
    )
    if not template:
        raise HTTPException(
            status_code=400,
            detail="Sales channel template could not be created.",
        )
    return template

@router.get("/templates", response_model=SalesChannelTemplateListResponse)
async def read_sales_channel_templates(
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取渠道模板列表.
    """
    templates = await sales_channel_template_service.get_multi(db=db, project_id=project.id, skip=skip, limit=limit)
    # Assuming get_multi doesn't return total count, we might need another service call for it
    # For now, let's assume total is not required or handled differently.
    return {"items": templates, "total": len(templates)} # Placeholder for total count

@router.get("/templates/{template_id}", response_model=SalesChannelTemplateResponse)
async def read_sales_channel_template(
    template_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
) -> Any:
    """
    获取单个渠道模板详情.
    """
    template = await sales_channel_template_service.get(db=db, id=template_id, project_id=project.id)
    if not template:
        raise HTTPException(
            status_code=404,
            detail="该渠道模板不存在",
        )
    return template

@router.put("/templates/{template_id}", response_model=SalesChannelTemplateResponse)
async def update_sales_channel_template(
    template_id: uuid.UUID,
    template_in: SalesChannelTemplateUpdate,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
) -> Any:
    """
    更新渠道模板.
    """
    db_template = await sales_channel_template_service.get(db=db, id=template_id, project_id=project.id)
    if not db_template:
        raise HTTPException(
            status_code=404,
            detail="该渠道模板不存在",
        )
    template = await sales_channel_template_service.update(db=db, db_obj=db_template, obj_in=template_in)
    return template

@router.delete("/templates/{template_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_sales_channel_template(
    template_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project)
) -> None:
    """
    删除渠道模板.
    """
    db_template = await sales_channel_template_service.get(db=db, id=template_id, project_id=project.id)
    if not db_template:
        raise HTTPException(status_code=404, detail="该渠道模板不存在")
        
    await sales_channel_template_service.remove(db=db, id=template_id)
    return None

class BatchCreateChannels(BaseModel):
    template_id: uuid.UUID
    store_ids: List[uuid.UUID] = Field(..., min_items=1)

@router.post("/channels/batch-create", status_code=status.HTTP_201_CREATED)
async def batch_create_sales_channels(
    data: BatchCreateChannels,
    db: AsyncSession = Depends(get_db),
    project: Project = Depends(get_current_project),
):
    """
    根据模板为多个门店批量创建销售渠道.
    Note: The underlying service logic for this needs to be refactored.
    """
    try:
        await sales_channel_template_service.batch_create_from_template(
            db=db, project_id=project.id, template_id=data.template_id, store_ids=data.store_ids
        )
        return {"message": "批量创建任务已开始"}
    except NotImplementedError:
        raise HTTPException(status_code=501, detail="此功能正在重构中，暂不可用")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
